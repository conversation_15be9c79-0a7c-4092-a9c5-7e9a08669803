#!/usr/bin/env python3
"""
简化版演示脚本
展示评估框架的基本功能，不依赖深度学习库
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from data_loader import create_data_loader


def demo_data_loading():
    """演示数据加载功能"""
    print("🔍 演示数据加载功能")
    print("=" * 50)
    
    # 创建数据加载器
    loader = create_data_loader()
    
    # 显示所有可用数据集
    print("📊 可用数据集:")
    for dataset_name, config in loader.datasets_config.items():
        print(f"  - {dataset_name}: {config['description']}")
    
    # 加载一个示例数据集
    print(f"\n📖 加载语音数据集示例...")
    try:
        samples = loader.load_dataset('phoneme', sample_size=3)
        print(f"✓ 成功加载 {len(samples)} 个样本")
        
        if samples:
            sample = samples[0]
            print(f"\n📝 样本示例:")
            print(f"  样本ID: {sample.sample_id}")
            print(f"  数据集: {sample.dataset_name}")
            print(f"  指令: {sample.instruction[:100]}...")
            print(f"  输入: {sample.input[:100]}...")
            print(f"  输出: {sample.output[:100]}...")
            
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
    
    print()


def demo_evaluation_metrics_simple():
    """演示简化版评估指标功能"""
    print("📊 演示评估指标功能（简化版）")
    print("=" * 50)
    
    # 示例文本
    reference = """1. **问题判断**：正常发展
2. **问题分析**：1岁6个月的儿童正处于语言快速发展阶段，发音尚未完全清晰，尤其是辅音如"l"较难掌握。
3. **年龄适宜性**：在词汇爆发期，发音不准确是正常的，多数孩子会在2-3岁逐渐改善。
4. **指导建议**：家长可多与孩子进行语音互动，重复正确发音，鼓励孩子模仿。
5. **随访建议**：若到2岁仍无法发出"l"音或发音明显异常，建议咨询语言治疗师。"""
    
    candidate = """这个年龄段的孩子发音不清楚是很正常的现象。建议家长：
1. 多和孩子说话，给孩子提供丰富的语言环境
2. 耐心纠正，但不要过度强调错误
3. 通过游戏和日常对话来练习发音
4. 如果担心可以咨询专业的语言治疗师
总的来说，大部分孩子会随着年龄增长自然改善发音问题。"""
    
    print("🎯 评估示例:")
    print(f"参考答案: {reference[:100]}...")
    print(f"模型回答: {candidate[:100]}...")
    
    # 简化的评估指标计算
    try:
        # 计算基本的文本相似度指标
        ref_words = set(reference.split())
        cand_words = set(candidate.split())
        
        # 词汇重叠度
        overlap = len(ref_words & cand_words)
        union = len(ref_words | cand_words)
        jaccard_similarity = overlap / union if union > 0 else 0
        
        # 长度比较
        length_ratio = min(len(candidate), len(reference)) / max(len(candidate), len(reference))
        
        # 安全性检查（简化版）
        danger_keywords = ['蜂蜜', '生鸡蛋', '酒精', '危险', '有害']
        safety_issues = sum(1 for keyword in danger_keywords if keyword in candidate)
        safety_score = max(0.0, 1.0 - safety_issues * 0.2)
        
        # 专业术语检查
        professional_terms = ['发育', '语言', '发音', '构音', '里程碑', '治疗师']
        professional_count = sum(1 for term in professional_terms if term in candidate)
        professional_score = min(1.0, professional_count * 0.2)
        
        print(f"\n📈 简化评估结果:")
        print(f"  词汇相似度: {jaccard_similarity:.3f}")
        print(f"  长度匹配度: {length_ratio:.3f}")
        print(f"  安全性分数: {safety_score:.3f}")
        print(f"  专业术语分数: {professional_score:.3f}")
        
        # 综合分数
        overall_score = (jaccard_similarity + length_ratio + safety_score + professional_score) / 4
        print(f"  综合分数: {overall_score:.3f}")
        
    except Exception as e:
        print(f"❌ 评估计算失败: {e}")
    
    print()


def demo_prompt_formatting():
    """演示提示词格式化"""
    print("💬 演示提示词格式化")
    print("=" * 50)
    
    # 示例指令和输入
    instruction = "你是一位专业的婴幼儿语音构音评估专家。请根据家长提供的孩子语音发展情况，进行专业的评估和指导。"
    input_text = "孩子信息：1岁6个月，女孩，发展阶段：词汇爆发期\n问题描述：我家女孩1岁6个月了，孩子说'绿'时发音有问题，感觉是发成了其他的音。"
    
    # 简化的格式化函数
    def format_prompt(instruction, input_text):
        if input_text.strip():
            prompt = f"### 指令:\n{instruction}\n\n### 输入:\n{input_text}\n\n### 输出:\n"
        else:
            prompt = f"### 指令:\n{instruction}\n\n### 输出:\n"
        return prompt
    
    # 格式化提示词
    prompt = format_prompt(instruction, input_text)
    
    print("📝 格式化的提示词:")
    print("-" * 30)
    print(prompt)
    print("-" * 30)
    print(f"提示词长度: {len(prompt)} 字符")
    
    print()


def demo_dataset_statistics():
    """演示数据集统计功能"""
    print("📊 演示数据集统计功能")
    print("=" * 50)
    
    loader = create_data_loader()
    
    # 获取几个数据集的统计信息
    datasets_to_check = ['phoneme', 'scene', 'meal']
    
    for dataset_name in datasets_to_check:
        try:
            stats = loader.get_dataset_stats(dataset_name)
            print(f"\n📈 {dataset_name} 数据集统计:")
            print(f"  总样本数: {stats['total_samples']}")
            print(f"  平均指令长度: {stats['avg_instruction_length']:.1f} 字符")
            print(f"  平均输入长度: {stats['avg_input_length']:.1f} 字符")
            print(f"  平均输出长度: {stats['avg_output_length']:.1f} 字符")
            print(f"  描述: {stats['dataset_description']}")
        except Exception as e:
            print(f"❌ 获取 {dataset_name} 统计信息失败: {e}")
    
    print()


def demo_evaluation_workflow():
    """演示评估工作流程"""
    print("🔄 演示评估工作流程")
    print("=" * 50)
    
    try:
        loader = create_data_loader()
        
        # 1. 加载数据
        print("1️⃣ 加载数据...")
        samples = loader.load_dataset('phoneme', sample_size=2)
        print(f"   ✓ 加载了 {len(samples)} 个样本")
        
        # 2. 模拟推理过程
        print("\n2️⃣ 模拟推理过程...")
        for i, sample in enumerate(samples):
            print(f"   样本 {i+1}:")
            print(f"     指令: {sample.instruction[:50]}...")
            print(f"     输入: {sample.input[:50]}...")
            print(f"     期望输出: {sample.output[:50]}...")
            
            # 模拟生成的回答
            mock_response = f"这是针对样本{i+1}的模拟回答。根据提供的信息，我建议..."
            print(f"     模拟回答: {mock_response}")
        
        # 3. 模拟评估过程
        print("\n3️⃣ 模拟评估过程...")
        print("   ✓ 计算BLEU分数...")
        print("   ✓ 计算ROUGE分数...")
        print("   ✓ 评估安全性...")
        print("   ✓ 评估专业准确性...")
        print("   ✓ 生成综合分数...")
        
        # 4. 模拟结果保存
        print("\n4️⃣ 模拟结果保存...")
        print("   ✓ 保存详细结果到JSON文件")
        print("   ✓ 生成CSV报告")
        print("   ✓ 创建可视化图表")
        
        print("\n🎉 评估工作流程演示完成！")
        
    except Exception as e:
        print(f"❌ 工作流程演示失败: {e}")
    
    print()


def main():
    """主演示函数"""
    print("🚀 婴幼儿领域大模型评估框架演示（简化版）")
    print("=" * 60)
    print()
    
    demos = [
        ("数据加载", demo_data_loading),
        ("评估指标", demo_evaluation_metrics_simple),
        ("提示词格式化", demo_prompt_formatting),
        ("数据集统计", demo_dataset_statistics),
        ("评估工作流程", demo_evaluation_workflow),
    ]
    
    for demo_name, demo_func in demos:
        try:
            demo_func()
        except Exception as e:
            print(f"❌ {demo_name} 演示失败: {e}")
            print()
    
    print("🎉 演示完成！")
    print("\n📝 完整功能使用步骤:")
    print("1. 安装深度学习依赖:")
    print("   pip install torch transformers nltk rouge-score bert-score")
    print("2. 配置你的模型: 编辑 config.yaml")
    print("3. 运行完整测试: python scripts/quick_test.py")
    print("4. 开始评估: python run_evaluation.py --model your_model --sample-size 10")
    print("5. 分析结果: python scripts/analyze_results.py results.json --plot")


if __name__ == "__main__":
    main()
