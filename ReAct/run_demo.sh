#!/bin/bash

# ReAct应用调用系统演示脚本

echo "🚀 ReAct应用调用系统演示"
echo "=========================="

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未找到，请先安装Python3"
    exit 1
fi

# 检查配置文件
if [ ! -f "app_caller_config.yaml" ]; then
    echo "❌ 配置文件 app_caller_config.yaml 未找到"
    exit 1
fi

# 显示菜单
echo "请选择演示模式:"
echo "1) 交互式演示"
echo "2) 批量测试"
echo "3) 创建测试用例"
echo "4) 模型评估"
echo "5) 生成训练数据"
echo "6) 退出"

read -p "请输入选择 (1-6): " choice

case $choice in
    1)
        echo "🎯 启动交互式演示..."
        python3 demo_react_app_caller.py --mode interactive
        ;;
    2)
        echo "🔄 启动批量测试..."
        python3 demo_react_app_caller.py --mode batch
        ;;
    3)
        echo "📝 创建测试用例..."
        python3 demo_react_app_caller.py --mode create-test
        echo "✅ 测试用例已创建: test_cases.json"
        ;;
    4)
        echo "📊 启动模型评估..."
        if [ ! -f "test_cases.json" ]; then
            echo "⚠️  测试用例文件不存在，先创建测试用例..."
            python3 demo_react_app_caller.py --mode create-test
        fi
        python3 demo_react_app_caller.py --mode eval --test-cases test_cases.json
        ;;
    5)
        echo "🏗️  生成训练数据..."
        python3 train_react_model.py --generate-data-only
        echo "✅ 训练数据已生成到 training_data/ 目录"
        ;;
    6)
        echo "👋 再见！"
        exit 0
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac

echo ""
echo "✅ 演示完成！"
