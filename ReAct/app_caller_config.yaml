# ReAct应用调用系统配置文件

# 模型配置
model:
  name: "qwen2.5-3b-instruct"  # 基础模型
  model_path: "~/qwen-finetune/models/Qwen/Qwen2.5-3B-Instruct"
  lora_path: null  # 微调后的LoRA权重路径
  max_length: 2048
  temperature: 0.1
  top_p: 0.9

# ReAct推理配置
react:
  max_iterations: 10  # 最大推理轮数
  max_thought_length: 512  # 思考步骤最大长度
  max_action_length: 256   # 行动步骤最大长度
  stop_tokens: ["Finish[", "Final Answer:"]
  
# 应用注册配置
applications:
  # 内置应用类型
  builtin_apps:
    - name: "calculator"
      description: "执行数学计算"
      function: "calculate"
      parameters:
        - name: "expression"
          type: "string"
          description: "数学表达式"
          required: true
    
    - name: "weather"
      description: "查询天气信息"
      function: "get_weather"
      parameters:
        - name: "location"
          type: "string"
          description: "地点名称"
          required: true
        - name: "date"
          type: "string"
          description: "日期(可选)"
          required: false
    
    - name: "search"
      description: "搜索信息"
      function: "web_search"
      parameters:
        - name: "query"
          type: "string"
          description: "搜索关键词"
          required: true
        - name: "num_results"
          type: "integer"
          description: "返回结果数量"
          required: false
          default: 5
    
    - name: "translator"
      description: "翻译文本"
      function: "translate"
      parameters:
        - name: "text"
          type: "string"
          description: "待翻译文本"
          required: true
        - name: "target_language"
          type: "string"
          description: "目标语言"
          required: true
        - name: "source_language"
          type: "string"
          description: "源语言(可选)"
          required: false
    
    - name: "email"
      description: "发送邮件"
      function: "send_email"
      parameters:
        - name: "recipient"
          type: "string"
          description: "收件人邮箱"
          required: true
        - name: "subject"
          type: "string"
          description: "邮件主题"
          required: true
        - name: "content"
          type: "string"
          description: "邮件内容"
          required: true
    
    - name: "calendar"
      description: "管理日程安排"
      function: "manage_calendar"
      parameters:
        - name: "action"
          type: "string"
          description: "操作类型: add, delete, query"
          required: true
        - name: "title"
          type: "string"
          description: "事件标题"
          required: false
        - name: "datetime"
          type: "string"
          description: "日期时间"
          required: false
        - name: "duration"
          type: "string"
          description: "持续时间"
          required: false

  # 外部应用配置
  external_apps:
    registry_file: "external_apps.json"
    auto_discovery: true
    api_timeout: 30

# 训练配置
training:
  data_dir: "training_data"
  output_dir: "react_model_output"
  
  # 数据集配置
  dataset:
    train_file: "react_train.json"
    validation_file: "react_validation.json"
    test_file: "react_test.json"
    max_samples: -1  # -1表示使用全部数据
  
  # 微调参数
  fine_tuning:
    learning_rate: 2e-5
    batch_size: 4
    gradient_accumulation_steps: 4
    num_epochs: 3
    warmup_ratio: 0.1
    weight_decay: 0.01
    max_grad_norm: 1.0
    
    # LoRA配置
    lora:
      r: 16
      alpha: 32
      dropout: 0.1
      target_modules: ["q_proj", "v_proj", "k_proj", "o_proj"]
    
    # 优化配置
    optimizer: "adamw"
    scheduler: "cosine"
    fp16: true
    gradient_checkpointing: true

# 评估配置
evaluation:
  metrics:
    - "intent_accuracy"      # 意图识别准确率
    - "app_selection_accuracy"  # 应用选择准确率
    - "parameter_extraction_f1"  # 参数提取F1分数
    - "end_to_end_success_rate"  # 端到端成功率
    - "reasoning_quality"     # 推理质量评分
  
  test_cases_file: "test_cases.json"
  output_file: "evaluation_results.json"

# 日志配置
logging:
  level: "INFO"
  file: "react_app_caller.log"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# 服务配置
service:
  host: "0.0.0.0"
  port: 8080
  debug: false
  cors_enabled: true
