"""
应用注册和管理模块
负责管理可调用的应用程序，包括内置应用和外部应用
"""

import json
import yaml
import logging
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)

@dataclass
class AppParameter:
    """应用参数定义"""
    name: str
    type: str  # string, integer, float, boolean, list, dict
    description: str
    required: bool = True
    default: Any = None
    
    def validate(self, value: Any) -> bool:
        """验证参数值是否符合要求"""
        if self.required and value is None:
            return False
        
        if value is None:
            return True
        
        # 类型检查
        type_mapping = {
            'string': str,
            'integer': int,
            'float': float,
            'boolean': bool,
            'list': list,
            'dict': dict
        }
        
        expected_type = type_mapping.get(self.type)
        if expected_type and not isinstance(value, expected_type):
            try:
                # 尝试类型转换
                if self.type == 'integer':
                    int(value)
                elif self.type == 'float':
                    float(value)
                elif self.type == 'boolean':
                    value.lower() in ['true', 'false', '1', '0']
                return True
            except (ValueError, AttributeError):
                return False
        
        return True

@dataclass
class AppDefinition:
    """应用定义"""
    name: str
    description: str
    function: str  # 函数名或API端点
    parameters: List[AppParameter]
    category: str = "general"
    version: str = "1.0.0"
    author: str = "system"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'name': self.name,
            'description': self.description,
            'function': self.function,
            'parameters': [asdict(param) for param in self.parameters],
            'category': self.category,
            'version': self.version,
            'author': self.author
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AppDefinition':
        """从字典创建应用定义"""
        parameters = [AppParameter(**param) for param in data.get('parameters', [])]
        return cls(
            name=data['name'],
            description=data['description'],
            function=data['function'],
            parameters=parameters,
            category=data.get('category', 'general'),
            version=data.get('version', '1.0.0'),
            author=data.get('author', 'system')
        )

class BaseApp(ABC):
    """应用基类"""
    
    def __init__(self, definition: AppDefinition):
        self.definition = definition
    
    @abstractmethod
    def execute(self, **kwargs) -> Dict[str, Any]:
        """执行应用功能"""
        pass
    
    def validate_parameters(self, **kwargs) -> tuple[bool, str]:
        """验证输入参数"""
        for param in self.definition.parameters:
            value = kwargs.get(param.name)
            if not param.validate(value):
                return False, f"Parameter '{param.name}' validation failed"
        return True, ""

class BuiltinApp(BaseApp):
    """内置应用"""
    
    def __init__(self, definition: AppDefinition, function: Callable):
        super().__init__(definition)
        self.function = function
    
    def execute(self, **kwargs) -> Dict[str, Any]:
        """执行内置应用功能"""
        valid, error = self.validate_parameters(**kwargs)
        if not valid:
            return {"success": False, "error": error}
        
        try:
            result = self.function(**kwargs)
            return {"success": True, "result": result}
        except Exception as e:
            logger.error(f"Error executing {self.definition.name}: {e}")
            return {"success": False, "error": str(e)}

class ExternalApp(BaseApp):
    """外部应用"""
    
    def __init__(self, definition: AppDefinition, api_config: Dict[str, Any]):
        super().__init__(definition)
        self.api_config = api_config
    
    def execute(self, **kwargs) -> Dict[str, Any]:
        """执行外部应用功能"""
        valid, error = self.validate_parameters(**kwargs)
        if not valid:
            return {"success": False, "error": error}
        
        # 这里可以实现HTTP API调用、RPC调用等
        # 暂时返回模拟结果
        return {
            "success": True, 
            "result": f"External app {self.definition.name} executed with {kwargs}"
        }

class AppRegistry:
    """应用注册表"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.apps: Dict[str, BaseApp] = {}
        self.config = {}
        
        if config_path:
            self.load_config(config_path)
    
    def load_config(self, config_path: str):
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                if config_path.endswith('.yaml') or config_path.endswith('.yml'):
                    self.config = yaml.safe_load(f)
                else:
                    self.config = json.load(f)
            
            self._load_builtin_apps()
            self._load_external_apps()
            
        except Exception as e:
            logger.error(f"Failed to load config from {config_path}: {e}")
    
    def _load_builtin_apps(self):
        """加载内置应用"""
        builtin_apps = self.config.get('applications', {}).get('builtin_apps', [])
        
        for app_config in builtin_apps:
            try:
                # 创建参数定义
                parameters = []
                for param_config in app_config.get('parameters', []):
                    param = AppParameter(
                        name=param_config['name'],
                        type=param_config['type'],
                        description=param_config['description'],
                        required=param_config.get('required', True),
                        default=param_config.get('default')
                    )
                    parameters.append(param)
                
                # 创建应用定义
                definition = AppDefinition(
                    name=app_config['name'],
                    description=app_config['description'],
                    function=app_config['function'],
                    parameters=parameters
                )
                
                # 获取对应的函数
                function = self._get_builtin_function(app_config['function'])
                if function:
                    app = BuiltinApp(definition, function)
                    self.register_app(app)
                    logger.info(f"Loaded builtin app: {app_config['name']}")
                
            except Exception as e:
                logger.error(f"Failed to load builtin app {app_config.get('name', 'unknown')}: {e}")
    
    def _load_external_apps(self):
        """加载外部应用"""
        external_config = self.config.get('applications', {}).get('external_apps', {})
        registry_file = external_config.get('registry_file')
        
        if registry_file:
            try:
                with open(registry_file, 'r', encoding='utf-8') as f:
                    external_apps = json.load(f)
                
                for app_config in external_apps:
                    definition = AppDefinition.from_dict(app_config)
                    app = ExternalApp(definition, app_config.get('api_config', {}))
                    self.register_app(app)
                    logger.info(f"Loaded external app: {app_config['name']}")
                    
            except FileNotFoundError:
                logger.warning(f"External apps registry file not found: {registry_file}")
            except Exception as e:
                logger.error(f"Failed to load external apps: {e}")
    
    def _get_builtin_function(self, function_name: str) -> Optional[Callable]:
        """获取内置函数"""
        # 这里可以导入实际的函数实现
        builtin_functions = {
            'calculate': self._mock_calculate,
            'get_weather': self._mock_get_weather,
            'web_search': self._mock_web_search,
            'translate': self._mock_translate,
            'send_email': self._mock_send_email,
            'manage_calendar': self._mock_manage_calendar,
        }
        return builtin_functions.get(function_name)
    
    # 模拟函数实现
    def _mock_calculate(self, expression: str) -> str:
        """模拟计算器功能"""
        try:
            # 简单的数学表达式计算
            result = eval(expression)
            return f"计算结果: {result}"
        except:
            return "计算表达式无效"
    
    def _mock_get_weather(self, location: str, date: str = None) -> str:
        """模拟天气查询功能"""
        return f"{location}的天气: 晴天，温度25°C"
    
    def _mock_web_search(self, query: str, num_results: int = 5) -> str:
        """模拟搜索功能"""
        return f"搜索'{query}'的结果: 找到{num_results}条相关信息"
    
    def _mock_translate(self, text: str, target_language: str, source_language: str = None) -> str:
        """模拟翻译功能"""
        return f"将'{text}'翻译为{target_language}: [翻译结果]"
    
    def _mock_send_email(self, recipient: str, subject: str, content: str) -> str:
        """模拟邮件发送功能"""
        return f"邮件已发送给{recipient}，主题: {subject}"
    
    def _mock_manage_calendar(self, action: str, **kwargs) -> str:
        """模拟日程管理功能"""
        return f"日程{action}操作完成"
    
    def register_app(self, app: BaseApp):
        """注册应用"""
        self.apps[app.definition.name] = app
        logger.info(f"Registered app: {app.definition.name}")
    
    def unregister_app(self, app_name: str):
        """注销应用"""
        if app_name in self.apps:
            del self.apps[app_name]
            logger.info(f"Unregistered app: {app_name}")
    
    def get_app(self, app_name: str) -> Optional[BaseApp]:
        """获取应用"""
        return self.apps.get(app_name)
    
    def list_apps(self) -> List[str]:
        """列出所有应用名称"""
        return list(self.apps.keys())
    
    def get_app_definitions(self) -> List[Dict[str, Any]]:
        """获取所有应用定义"""
        return [app.definition.to_dict() for app in self.apps.values()]
    
    def execute_app(self, app_name: str, **kwargs) -> Dict[str, Any]:
        """执行应用"""
        app = self.get_app(app_name)
        if not app:
            return {"success": False, "error": f"App '{app_name}' not found"}
        
        return app.execute(**kwargs)
