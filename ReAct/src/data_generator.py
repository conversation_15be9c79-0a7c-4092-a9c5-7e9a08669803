"""
训练数据生成器
生成ReAct格式的训练数据，包含用户意图、推理过程、应用调用等
"""

import json
import random
import logging
from typing import List, Dict, Any, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class TrainingExample:
    """训练样本"""
    user_query: str
    system_prompt: str
    reasoning_steps: List[str]
    final_answer: str
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'user_query': self.user_query,
            'system_prompt': self.system_prompt,
            'reasoning_steps': self.reasoning_steps,
            'final_answer': self.final_answer
        }
    
    def to_conversation_format(self) -> Dict[str, Any]:
        """转换为对话格式"""
        conversation = [
            {"role": "system", "content": self.system_prompt},
            {"role": "user", "content": self.user_query}
        ]
        
        # 将推理步骤合并为assistant的响应
        assistant_response = "\n".join(self.reasoning_steps)
        if self.final_answer:
            assistant_response += f"\nFinal Answer: {self.final_answer}"
        
        conversation.append({"role": "assistant", "content": assistant_response})
        
        return {"messages": conversation}

class DataGenerator:
    """训练数据生成器"""
    
    def __init__(self, app_registry, system_prompt: str):
        self.app_registry = app_registry
        self.system_prompt = system_prompt
        
        # 预定义的训练模板
        self.templates = self._load_templates()
    
    def _load_templates(self) -> List[Dict[str, Any]]:
        """加载训练模板"""
        return [
            # 计算器模板
            {
                "app": "calculator",
                "patterns": [
                    "帮我计算 {expression}",
                    "请算一下 {expression} 等于多少",
                    "{expression} 的结果是什么",
                    "计算 {expression}",
                ],
                "expressions": [
                    "25 * 4", "100 / 5", "15 + 37", "89 - 23",
                    "2 ** 8", "sqrt(144)", "sin(30)", "log(100)"
                ]
            },
            
            # 天气查询模板
            {
                "app": "weather",
                "patterns": [
                    "{location}的天气怎么样",
                    "查询{location}的天气",
                    "今天{location}天气如何",
                    "{location}{date}的天气预报",
                ],
                "locations": ["北京", "上海", "广州", "深圳", "杭州", "成都"],
                "dates": ["今天", "明天", "后天", "这周末"]
            },
            
            # 搜索模板
            {
                "app": "search",
                "patterns": [
                    "搜索{query}",
                    "帮我查找{query}的信息",
                    "我想了解{query}",
                    "关于{query}的资料",
                ],
                "queries": [
                    "人工智能发展历史", "Python编程教程", "机器学习算法",
                    "深度学习框架", "自然语言处理", "计算机视觉"
                ]
            },
            
            # 翻译模板
            {
                "app": "translator",
                "patterns": [
                    "把'{text}'翻译成{target_language}",
                    "请将'{text}'翻译为{target_language}",
                    "'{text}'的{target_language}翻译是什么",
                    "翻译：{text} -> {target_language}",
                ],
                "texts": [
                    "Hello, how are you?", "Good morning", "Thank you very much",
                    "I love programming", "Machine learning is fascinating"
                ],
                "target_languages": ["中文", "英文", "日文", "法文", "德文"]
            },
            
            # 邮件模板
            {
                "app": "email",
                "patterns": [
                    "发邮件给{recipient}，主题是{subject}",
                    "给{recipient}发送邮件：{subject}",
                    "向{recipient}发送关于{subject}的邮件",
                ],
                "recipients": ["<EMAIL>", "<EMAIL>", "<EMAIL>"],
                "subjects": ["会议通知", "项目进展", "工作汇报", "合作提案"]
            },
            
            # 日程管理模板
            {
                "app": "calendar",
                "patterns": [
                    "在{datetime}安排{title}",
                    "添加日程：{datetime} {title}",
                    "预约{datetime}的{title}",
                    "查询{datetime}的日程安排",
                ],
                "datetimes": ["明天上午10点", "下周一下午2点", "本周五晚上7点"],
                "titles": ["团队会议", "客户拜访", "项目评审", "培训课程"]
            }
        ]
    
    def generate_single_app_examples(self, num_examples: int = 100) -> List[TrainingExample]:
        """生成单应用调用的训练样本"""
        examples = []
        
        for template in self.templates:
            app_name = template["app"]
            patterns = template["patterns"]
            
            # 为每个应用生成样本
            app_examples = num_examples // len(self.templates)
            
            for _ in range(app_examples):
                example = self._generate_single_app_example(template)
                if example:
                    examples.append(example)
        
        return examples
    
    def _generate_single_app_example(self, template: Dict[str, Any]) -> TrainingExample:
        """生成单个应用调用样本"""
        app_name = template["app"]
        pattern = random.choice(template["patterns"])
        
        # 根据应用类型生成参数
        if app_name == "calculator":
            expression = random.choice(template["expressions"])
            user_query = pattern.format(expression=expression)
            params = {"expression": expression}
            expected_result = f"计算结果: {expression} = [结果]"
            
        elif app_name == "weather":
            location = random.choice(template["locations"])
            date = random.choice(template.get("dates", [""]))
            user_query = pattern.format(location=location, date=date)
            params = {"location": location}
            if date and "{date}" in pattern:
                params["date"] = date
            expected_result = f"{location}的天气: 晴天，温度25°C"
            
        elif app_name == "search":
            query = random.choice(template["queries"])
            user_query = pattern.format(query=query)
            params = {"query": query}
            expected_result = f"搜索'{query}'的结果: 找到5条相关信息"
            
        elif app_name == "translator":
            text = random.choice(template["texts"])
            target_language = random.choice(template["target_languages"])
            user_query = pattern.format(text=text, target_language=target_language)
            params = {"text": text, "target_language": target_language}
            expected_result = f"将'{text}'翻译为{target_language}: [翻译结果]"
            
        elif app_name == "email":
            recipient = random.choice(template["recipients"])
            subject = random.choice(template["subjects"])
            user_query = pattern.format(recipient=recipient, subject=subject)
            params = {"recipient": recipient, "subject": subject, "content": f"关于{subject}的邮件内容"}
            expected_result = f"邮件已发送给{recipient}，主题: {subject}"
            
        elif app_name == "calendar":
            datetime_str = random.choice(template["datetimes"])
            title = random.choice(template["titles"])
            user_query = pattern.format(datetime=datetime_str, title=title)
            params = {"action": "add", "title": title, "datetime": datetime_str}
            expected_result = f"日程add操作完成"
            
        else:
            return None
        
        # 生成推理步骤
        reasoning_steps = [
            f"Thought 1: 用户需要使用{app_name}应用来完成任务。",
            f"Action 1: CallApp[{app_name}]",
            f"参数: {json.dumps(params, ensure_ascii=False)}",
            f"Observation 1: {expected_result}",
            f"Thought 2: 任务完成，可以给出最终答案。",
            f"Action 2: Finish[{expected_result}]"
        ]
        
        return TrainingExample(
            user_query=user_query,
            system_prompt=self.system_prompt,
            reasoning_steps=reasoning_steps,
            final_answer=expected_result
        )
    
    def generate_multi_app_examples(self, num_examples: int = 50) -> List[TrainingExample]:
        """生成多应用调用的训练样本"""
        examples = []
        
        # 预定义一些多步骤任务
        multi_step_templates = [
            {
                "query": "帮我计算25*4的结果，然后搜索这个数字的含义",
                "steps": [
                    ("calculator", {"expression": "25*4"}),
                    ("search", {"query": "100的含义"})
                ]
            },
            {
                "query": "查询北京的天气，如果是晴天就发邮件通知****************",
                "steps": [
                    ("weather", {"location": "北京"}),
                    ("email", {"recipient": "<EMAIL>", "subject": "天气通知", "content": "今天北京天气晴朗"})
                ]
            },
            {
                "query": "翻译'Hello World'为中文，然后搜索相关的编程教程",
                "steps": [
                    ("translator", {"text": "Hello World", "target_language": "中文"}),
                    ("search", {"query": "Hello World编程教程"})
                ]
            }
        ]
        
        for template in multi_step_templates:
            for _ in range(num_examples // len(multi_step_templates)):
                example = self._generate_multi_app_example(template)
                if example:
                    examples.append(example)
        
        return examples
    
    def _generate_multi_app_example(self, template: Dict[str, Any]) -> TrainingExample:
        """生成多应用调用样本"""
        user_query = template["query"]
        steps = template["steps"]
        
        reasoning_steps = []
        step_num = 1
        
        for app_name, params in steps:
            reasoning_steps.extend([
                f"Thought {step_num}: 需要使用{app_name}应用。",
                f"Action {step_num}: CallApp[{app_name}]",
                f"参数: {json.dumps(params, ensure_ascii=False)}",
                f"Observation {step_num}: [模拟{app_name}应用的执行结果]"
            ])
            step_num += 1
        
        reasoning_steps.extend([
            f"Thought {step_num}: 所有步骤完成，可以给出最终答案。",
            f"Action {step_num}: Finish[任务已完成，执行了{len(steps)}个步骤]"
        ])
        
        return TrainingExample(
            user_query=user_query,
            system_prompt=self.system_prompt,
            reasoning_steps=reasoning_steps,
            final_answer=f"任务已完成，执行了{len(steps)}个步骤"
        )
    
    def generate_dataset(self, 
                        single_app_examples: int = 800,
                        multi_app_examples: int = 200) -> List[TrainingExample]:
        """生成完整的训练数据集"""
        logger.info(f"Generating {single_app_examples} single-app examples...")
        single_examples = self.generate_single_app_examples(single_app_examples)
        
        logger.info(f"Generating {multi_app_examples} multi-app examples...")
        multi_examples = self.generate_multi_app_examples(multi_app_examples)
        
        all_examples = single_examples + multi_examples
        random.shuffle(all_examples)
        
        logger.info(f"Generated {len(all_examples)} training examples")
        return all_examples
    
    def save_dataset(self, examples: List[TrainingExample], output_file: str):
        """保存数据集"""
        # 转换为对话格式
        conversations = [example.to_conversation_format() for example in examples]
        
        with open(output_file, 'w', encoding='utf-8') as f:
            for conversation in conversations:
                f.write(json.dumps(conversation, ensure_ascii=False) + '\n')
        
        logger.info(f"Saved {len(conversations)} examples to {output_file}")
    
    def split_dataset(self, examples: List[TrainingExample], 
                     train_ratio: float = 0.8, 
                     val_ratio: float = 0.1) -> Tuple[List[TrainingExample], List[TrainingExample], List[TrainingExample]]:
        """分割数据集"""
        random.shuffle(examples)
        
        total = len(examples)
        train_size = int(total * train_ratio)
        val_size = int(total * val_ratio)
        
        train_examples = examples[:train_size]
        val_examples = examples[train_size:train_size + val_size]
        test_examples = examples[train_size + val_size:]
        
        return train_examples, val_examples, test_examples
