"""
ReAct应用调用系统主类
整合所有组件，提供统一的接口
"""

import yaml
import logging
from typing import Dict, Any, Optional

from .app_registry import AppRegistry
from .model_interface import create_model_interface, ModelInterface
from .react_engine import ReActEngine, ReActResult

logger = logging.getLogger(__name__)

class ReActAppCaller:
    """ReAct应用调用系统"""
    
    def __init__(self, config_path: str):
        """初始化系统"""
        self.config = self._load_config(config_path)
        self._setup_logging()
        
        # 初始化组件
        self.app_registry = AppRegistry(config_path)
        self.model = create_model_interface(self.config)
        self.react_engine = ReActEngine(self.model, self.app_registry, self.config)
        
        logger.info("ReAct App Caller system initialized")
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            return config
        except Exception as e:
            logger.error(f"Failed to load config from {config_path}: {e}")
            raise
    
    def _setup_logging(self):
        """设置日志"""
        log_config = self.config.get('logging', {})
        log_level = getattr(logging, log_config.get('level', 'INFO'))
        log_format = log_config.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        log_file = log_config.get('file')
        
        logging.basicConfig(
            level=log_level,
            format=log_format,
            filename=log_file
        )
    
    def process_query(self, user_query: str) -> ReActResult:
        """处理用户查询"""
        logger.info(f"Processing user query: {user_query}")
        
        try:
            result = self.react_engine.process_query(user_query)
            logger.info(f"Query processed successfully: {result.success}")
            return result
        except Exception as e:
            logger.error(f"Error processing query: {e}")
            return ReActResult(
                success=False,
                final_answer="",
                steps=[],
                error_message=str(e)
            )
    
    def get_available_apps(self) -> list:
        """获取可用应用列表"""
        return self.app_registry.list_apps()
    
    def get_app_definitions(self) -> list:
        """获取应用定义"""
        return self.app_registry.get_app_definitions()
    
    def is_ready(self) -> bool:
        """检查系统是否就绪"""
        return (
            self.model.is_available() and
            len(self.app_registry.list_apps()) > 0
        )
