"""
ReAct模型评估器
评估模型在应用调用任务上的表现
"""

import json
import logging
import re
from typing import List, Dict, Any, Tuple
from dataclasses import dataclass
from collections import defaultdict

from .app_caller import ReActAppCaller
from .react_engine import ReActResult

logger = logging.getLogger(__name__)

@dataclass
class EvaluationMetrics:
    """评估指标"""
    intent_accuracy: float = 0.0
    app_selection_accuracy: float = 0.0
    parameter_extraction_f1: float = 0.0
    end_to_end_success_rate: float = 0.0
    reasoning_quality: float = 0.0
    
    def to_dict(self) -> Dict[str, float]:
        return {
            'intent_accuracy': self.intent_accuracy,
            'app_selection_accuracy': self.app_selection_accuracy,
            'parameter_extraction_f1': self.parameter_extraction_f1,
            'end_to_end_success_rate': self.end_to_end_success_rate,
            'reasoning_quality': self.reasoning_quality
        }

@dataclass
class TestCase:
    """测试用例"""
    id: str
    user_query: str
    expected_app: str
    expected_parameters: Dict[str, Any]
    expected_result: str
    category: str = "general"
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TestCase':
        return cls(
            id=data['id'],
            user_query=data['user_query'],
            expected_app=data['expected_app'],
            expected_parameters=data['expected_parameters'],
            expected_result=data['expected_result'],
            category=data.get('category', 'general')
        )

class ReActEvaluator:
    """ReAct模型评估器"""
    
    def __init__(self, app_caller: ReActAppCaller):
        self.app_caller = app_caller
    
    def evaluate(self, test_cases: List[TestCase]) -> EvaluationMetrics:
        """评估模型性能"""
        logger.info(f"Evaluating model on {len(test_cases)} test cases")
        
        results = []
        for test_case in test_cases:
            result = self._evaluate_single_case(test_case)
            results.append(result)
        
        # 计算总体指标
        metrics = self._calculate_metrics(results)
        
        logger.info(f"Evaluation completed. Metrics: {metrics.to_dict()}")
        return metrics
    
    def _evaluate_single_case(self, test_case: TestCase) -> Dict[str, Any]:
        """评估单个测试用例"""
        try:
            # 执行推理
            react_result = self.app_caller.process_query(test_case.user_query)
            
            # 分析结果
            analysis = self._analyze_result(test_case, react_result)
            
            return {
                'test_case': test_case,
                'react_result': react_result,
                'analysis': analysis
            }
            
        except Exception as e:
            logger.error(f"Error evaluating test case {test_case.id}: {e}")
            return {
                'test_case': test_case,
                'react_result': None,
                'analysis': {
                    'intent_correct': False,
                    'app_correct': False,
                    'parameters_correct': False,
                    'end_to_end_success': False,
                    'reasoning_score': 0.0,
                    'error': str(e)
                }
            }
    
    def _analyze_result(self, test_case: TestCase, react_result: ReActResult) -> Dict[str, Any]:
        """分析推理结果"""
        analysis = {
            'intent_correct': False,
            'app_correct': False,
            'parameters_correct': False,
            'end_to_end_success': False,
            'reasoning_score': 0.0
        }
        
        if not react_result.success:
            return analysis
        
        # 分析应用调用步骤
        app_calls = self._extract_app_calls(react_result.steps)
        
        # 检查应用选择
        if app_calls:
            first_app_call = app_calls[0]
            analysis['app_correct'] = first_app_call['app'] == test_case.expected_app
            
            # 检查参数提取
            if analysis['app_correct']:
                analysis['parameters_correct'] = self._compare_parameters(
                    first_app_call['parameters'], 
                    test_case.expected_parameters
                )
        
        # 检查意图理解（基于应用选择的正确性）
        analysis['intent_correct'] = analysis['app_correct']
        
        # 检查端到端成功率
        analysis['end_to_end_success'] = (
            analysis['app_correct'] and 
            analysis['parameters_correct'] and
            react_result.success
        )
        
        # 评估推理质量
        analysis['reasoning_score'] = self._evaluate_reasoning_quality(react_result.steps)
        
        return analysis
    
    def _extract_app_calls(self, steps: List) -> List[Dict[str, Any]]:
        """从推理步骤中提取应用调用"""
        app_calls = []
        
        for step in steps:
            if hasattr(step, 'action_type') and step.action_type.value == 'CallApp':
                # 解析应用名称和参数
                action_input = step.action_input
                if '|' in action_input:
                    app_name, params_str = action_input.split('|', 1)
                    try:
                        parameters = json.loads(params_str)
                        app_calls.append({
                            'app': app_name,
                            'parameters': parameters
                        })
                    except json.JSONDecodeError:
                        logger.warning(f"Failed to parse parameters: {params_str}")
        
        return app_calls
    
    def _compare_parameters(self, actual: Dict[str, Any], expected: Dict[str, Any]) -> bool:
        """比较参数是否匹配"""
        # 检查必需参数是否都存在
        for key, value in expected.items():
            if key not in actual:
                return False
            
            # 简单的值比较（可以根据需要扩展）
            if str(actual[key]).lower() != str(value).lower():
                return False
        
        return True
    
    def _evaluate_reasoning_quality(self, steps: List) -> float:
        """评估推理质量"""
        if not steps:
            return 0.0
        
        score = 0.0
        max_score = 0.0
        
        for step in steps:
            if hasattr(step, 'thought'):
                max_score += 1.0
                
                # 检查思考步骤的质量
                thought = step.thought.lower()
                
                # 包含关键词加分
                if any(keyword in thought for keyword in ['需要', '应该', '使用', '调用']):
                    score += 0.3
                
                # 逻辑清晰加分
                if any(keyword in thought for keyword in ['因为', '所以', '然后', '接下来']):
                    score += 0.3
                
                # 具体明确加分
                if len(thought.split()) >= 5:  # 思考内容足够详细
                    score += 0.4
        
        return score / max_score if max_score > 0 else 0.0
    
    def _calculate_metrics(self, results: List[Dict[str, Any]]) -> EvaluationMetrics:
        """计算总体评估指标"""
        total_cases = len(results)
        if total_cases == 0:
            return EvaluationMetrics()
        
        intent_correct = sum(1 for r in results if r['analysis'].get('intent_correct', False))
        app_correct = sum(1 for r in results if r['analysis'].get('app_correct', False))
        params_correct = sum(1 for r in results if r['analysis'].get('parameters_correct', False))
        end_to_end_success = sum(1 for r in results if r['analysis'].get('end_to_end_success', False))
        
        # 计算参数提取F1分数（简化版本）
        param_f1 = params_correct / total_cases if total_cases > 0 else 0.0
        
        # 计算推理质量平均分
        reasoning_scores = [r['analysis'].get('reasoning_score', 0.0) for r in results]
        avg_reasoning_score = sum(reasoning_scores) / len(reasoning_scores) if reasoning_scores else 0.0
        
        return EvaluationMetrics(
            intent_accuracy=intent_correct / total_cases,
            app_selection_accuracy=app_correct / total_cases,
            parameter_extraction_f1=param_f1,
            end_to_end_success_rate=end_to_end_success / total_cases,
            reasoning_quality=avg_reasoning_score
        )
    
    def generate_detailed_report(self, test_cases: List[TestCase], 
                               output_file: str = None) -> Dict[str, Any]:
        """生成详细的评估报告"""
        results = []
        for test_case in test_cases:
            result = self._evaluate_single_case(test_case)
            results.append(result)
        
        # 计算指标
        metrics = self._calculate_metrics(results)
        
        # 按类别分析
        category_analysis = defaultdict(list)
        for result in results:
            category = result['test_case'].category
            category_analysis[category].append(result)
        
        category_metrics = {}
        for category, cat_results in category_analysis.items():
            category_metrics[category] = self._calculate_metrics(cat_results).to_dict()
        
        # 错误分析
        error_cases = [r for r in results if not r['analysis'].get('end_to_end_success', False)]
        error_analysis = self._analyze_errors(error_cases)
        
        report = {
            'overall_metrics': metrics.to_dict(),
            'category_metrics': category_metrics,
            'error_analysis': error_analysis,
            'total_cases': len(test_cases),
            'successful_cases': len([r for r in results if r['analysis'].get('end_to_end_success', False)]),
            'failed_cases': len(error_cases)
        }
        
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            logger.info(f"Detailed report saved to {output_file}")
        
        return report
    
    def _analyze_errors(self, error_cases: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析错误案例"""
        error_types = defaultdict(int)
        
        for case in error_cases:
            analysis = case['analysis']
            
            if not analysis.get('app_correct', False):
                error_types['wrong_app_selection'] += 1
            elif not analysis.get('parameters_correct', False):
                error_types['wrong_parameters'] += 1
            elif 'error' in analysis:
                error_types['execution_error'] += 1
            else:
                error_types['other'] += 1
        
        return dict(error_types)

def load_test_cases(file_path: str) -> List[TestCase]:
    """加载测试用例"""
    test_cases = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        for item in data:
            test_case = TestCase.from_dict(item)
            test_cases.append(test_case)
        
        logger.info(f"Loaded {len(test_cases)} test cases from {file_path}")
        
    except Exception as e:
        logger.error(f"Failed to load test cases from {file_path}: {e}")
    
    return test_cases
