"""
模型接口
支持不同类型的语言模型，包括本地模型和API模型
"""

import torch
import logging
from typing import List, Dict, Any, Optional
from abc import ABC, abstractmethod
from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import PeftModel

logger = logging.getLogger(__name__)

class ModelInterface(ABC):
    """模型接口基类"""
    
    @abstractmethod
    def generate(self, messages: List[Dict[str, str]]) -> str:
        """生成响应"""
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """检查模型是否可用"""
        pass

class LocalModelInterface(ModelInterface):
    """本地模型接口"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.model = None
        self.tokenizer = None
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        self._load_model()
    
    def _load_model(self):
        """加载模型"""
        try:
            model_path = self.config['model']['model_path']
            lora_path = self.config['model'].get('lora_path')
            
            logger.info(f"Loading model from {model_path}")
            
            # 加载tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(
                model_path,
                trust_remote_code=True,
                padding_side="left"
            )
            
            # 设置pad_token
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            # 加载模型
            self.model = AutoModelForCausalLM.from_pretrained(
                model_path,
                torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
                device_map="auto" if torch.cuda.is_available() else None,
                trust_remote_code=True
            )
            
            # 加载LoRA权重（如果有）
            if lora_path:
                logger.info(f"Loading LoRA weights from {lora_path}")
                self.model = PeftModel.from_pretrained(self.model, lora_path)
                self.model = self.model.merge_and_unload()
            
            self.model.eval()
            logger.info("Model loaded successfully")
            
        except Exception as e:
            logger.error(f"Failed to load model: {e}")
            raise
    
    def generate(self, messages: List[Dict[str, str]]) -> str:
        """生成响应"""
        if not self.is_available():
            raise RuntimeError("Model is not available")
        
        try:
            # 构建输入文本
            input_text = self._format_messages(messages)
            
            # Tokenize
            inputs = self.tokenizer(
                input_text,
                return_tensors="pt",
                truncation=True,
                max_length=self.config['model'].get('max_length', 2048)
            ).to(self.device)
            
            # 生成参数
            generation_config = {
                'max_new_tokens': 512,
                'temperature': self.config['model'].get('temperature', 0.1),
                'top_p': self.config['model'].get('top_p', 0.9),
                'do_sample': True,
                'pad_token_id': self.tokenizer.pad_token_id,
                'eos_token_id': self.tokenizer.eos_token_id,
            }
            
            # 生成响应
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    **generation_config
                )
            
            # 解码响应
            response = self.tokenizer.decode(
                outputs[0][inputs['input_ids'].shape[1]:],
                skip_special_tokens=True
            )
            
            return response.strip()
            
        except Exception as e:
            logger.error(f"Error generating response: {e}")
            raise
    
    def _format_messages(self, messages: List[Dict[str, str]]) -> str:
        """格式化消息为模型输入"""
        formatted_text = ""
        
        for message in messages:
            role = message['role']
            content = message['content']
            
            if role == 'system':
                formatted_text += f"<|im_start|>system\n{content}<|im_end|>\n"
            elif role == 'user':
                formatted_text += f"<|im_start|>user\n{content}<|im_end|>\n"
            elif role == 'assistant':
                formatted_text += f"<|im_start|>assistant\n{content}<|im_end|>\n"
        
        # 添加assistant开始标记
        formatted_text += "<|im_start|>assistant\n"
        
        return formatted_text
    
    def is_available(self) -> bool:
        """检查模型是否可用"""
        return self.model is not None and self.tokenizer is not None

class APIModelInterface(ModelInterface):
    """API模型接口（如OpenAI GPT）"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.api_key = config.get('api_key')
        self.model_name = config.get('model_name', 'gpt-3.5-turbo')
        
        # 这里可以初始化API客户端
        # 例如: self.client = openai.OpenAI(api_key=self.api_key)
    
    def generate(self, messages: List[Dict[str, str]]) -> str:
        """生成响应"""
        # 这里实现API调用
        # 暂时返回模拟响应
        return "API model response (not implemented)"
    
    def is_available(self) -> bool:
        """检查模型是否可用"""
        return self.api_key is not None

def create_model_interface(config: Dict[str, Any]) -> ModelInterface:
    """创建模型接口"""
    model_type = config.get('model', {}).get('type', 'local')
    
    if model_type == 'local':
        return LocalModelInterface(config)
    elif model_type == 'api':
        return APIModelInterface(config)
    else:
        raise ValueError(f"Unsupported model type: {model_type}")
