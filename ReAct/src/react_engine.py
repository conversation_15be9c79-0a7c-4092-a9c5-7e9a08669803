"""
ReAct推理引擎
实现思考-行动循环，包括意图识别、应用选择、参数提取等
"""

import re
import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from .app_registry import AppRegistry
from .model_interface import ModelInterface

logger = logging.getLogger(__name__)

class ActionType(Enum):
    """行动类型"""
    SEARCH = "Search"
    LOOKUP = "Lookup"
    CALL_APP = "CallApp"
    FINISH = "Finish"

@dataclass
class ReActStep:
    """ReAct推理步骤"""
    step_num: int
    thought: str
    action: str
    action_type: ActionType
    action_input: str
    observation: str
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'step_num': self.step_num,
            'thought': self.thought,
            'action': self.action,
            'action_type': self.action_type.value,
            'action_input': self.action_input,
            'observation': self.observation
        }

@dataclass
class ReActResult:
    """ReAct推理结果"""
    success: bool
    final_answer: str
    steps: List[ReActStep]
    error_message: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'success': self.success,
            'final_answer': self.final_answer,
            'steps': [step.to_dict() for step in self.steps],
            'error_message': self.error_message
        }

class ReActEngine:
    """ReAct推理引擎"""
    
    def __init__(self, model: ModelInterface, app_registry: AppRegistry, config: Dict[str, Any]):
        self.model = model
        self.app_registry = app_registry
        self.config = config
        
        # 推理配置
        self.max_iterations = config.get('react', {}).get('max_iterations', 10)
        self.max_thought_length = config.get('react', {}).get('max_thought_length', 512)
        self.max_action_length = config.get('react', {}).get('max_action_length', 256)
        self.stop_tokens = config.get('react', {}).get('stop_tokens', ["Finish[", "Final Answer:"])
        
        # 构建系统提示
        self.system_prompt = self._build_system_prompt()
    
    def _build_system_prompt(self) -> str:
        """构建系统提示"""
        app_descriptions = []
        for app_def in self.app_registry.get_app_definitions():
            params_desc = []
            for param in app_def['parameters']:
                param_str = f"{param['name']} ({param['type']})"
                if param['required']:
                    param_str += " [必需]"
                param_str += f": {param['description']}"
                params_desc.append(param_str)
            
            app_desc = f"- {app_def['name']}: {app_def['description']}\n  参数: {', '.join(params_desc)}"
            app_descriptions.append(app_desc)
        
        apps_text = "\n".join(app_descriptions)
        
        return f"""你是一个智能助手，能够理解用户的需求并调用相应的应用程序来完成任务。

可用的应用程序:
{apps_text}

你需要使用ReAct (Reasoning and Acting) 方法来解决问题：
1. 思考 (Thought): 分析用户需求，确定需要采取的行动
2. 行动 (Action): 选择合适的应用程序并提供参数
3. 观察 (Observation): 查看应用程序的执行结果
4. 重复上述过程直到完成任务

行动格式:
- CallApp[应用名称]: 调用指定的应用程序
  参数格式: {{"param1": "value1", "param2": "value2"}}
- Finish[最终答案]: 完成任务并给出最终答案

示例:
用户: 帮我计算 25 * 4 的结果
Thought 1: 用户需要计算数学表达式，我应该使用计算器应用。
Action 1: CallApp[calculator]
参数: {{"expression": "25 * 4"}}
Observation 1: 计算结果: 100
Thought 2: 计算完成，结果是100。
Action 2: Finish[25 * 4 的计算结果是 100]

现在开始处理用户的请求。记住要逐步思考，选择合适的应用程序，并根据结果给出最终答案。"""
    
    def process_query(self, user_query: str) -> ReActResult:
        """处理用户查询"""
        logger.info(f"Processing query: {user_query}")
        
        steps = []
        conversation_history = [
            {"role": "system", "content": self.system_prompt},
            {"role": "user", "content": user_query}
        ]
        
        for iteration in range(self.max_iterations):
            try:
                # 生成下一步推理
                response = self.model.generate(conversation_history)
                logger.debug(f"Model response (iteration {iteration + 1}): {response}")
                
                # 解析推理步骤
                step = self._parse_response(response, iteration + 1)
                if not step:
                    return ReActResult(
                        success=False,
                        final_answer="",
                        steps=steps,
                        error_message="Failed to parse model response"
                    )
                
                steps.append(step)
                
                # 检查是否完成
                if step.action_type == ActionType.FINISH:
                    return ReActResult(
                        success=True,
                        final_answer=step.action_input,
                        steps=steps
                    )
                
                # 执行行动并获取观察结果
                observation = self._execute_action(step)
                step.observation = observation
                
                # 更新对话历史
                conversation_history.append({
                    "role": "assistant", 
                    "content": f"Thought {step.step_num}: {step.thought}\nAction {step.step_num}: {step.action}\nObservation {step.step_num}: {step.observation}"
                })
                
            except Exception as e:
                logger.error(f"Error in iteration {iteration + 1}: {e}")
                return ReActResult(
                    success=False,
                    final_answer="",
                    steps=steps,
                    error_message=str(e)
                )
        
        # 达到最大迭代次数
        return ReActResult(
            success=False,
            final_answer="",
            steps=steps,
            error_message="Reached maximum iterations without completion"
        )
    
    def _parse_response(self, response: str, step_num: int) -> Optional[ReActStep]:
        """解析模型响应"""
        try:
            # 提取思考部分
            thought_match = re.search(r'Thought\s*\d*\s*:?\s*(.*?)(?=Action|\n\n|$)', response, re.DOTALL)
            thought = thought_match.group(1).strip() if thought_match else ""
            
            # 提取行动部分
            action_match = re.search(r'Action\s*\d*\s*:?\s*(.*?)(?=Observation|\n\n|$)', response, re.DOTALL)
            if not action_match:
                return None
            
            action_text = action_match.group(1).strip()
            
            # 解析行动类型和输入
            action_type, action_input = self._parse_action(action_text)
            if not action_type:
                return None
            
            return ReActStep(
                step_num=step_num,
                thought=thought,
                action=action_text,
                action_type=action_type,
                action_input=action_input,
                observation=""  # 将在执行后填充
            )
            
        except Exception as e:
            logger.error(f"Error parsing response: {e}")
            return None
    
    def _parse_action(self, action_text: str) -> Tuple[Optional[ActionType], str]:
        """解析行动文本"""
        # CallApp[app_name]
        call_app_match = re.search(r'CallApp\[([^\]]+)\]', action_text)
        if call_app_match:
            app_name = call_app_match.group(1)
            # 提取参数
            param_match = re.search(r'参数\s*:?\s*(\{.*?\})', action_text, re.DOTALL)
            if param_match:
                return ActionType.CALL_APP, f"{app_name}|{param_match.group(1)}"
            else:
                return ActionType.CALL_APP, f"{app_name}|{{}}"
        
        # Finish[answer]
        finish_match = re.search(r'Finish\[([^\]]*)\]', action_text)
        if finish_match:
            return ActionType.FINISH, finish_match.group(1)
        
        # Search[query] (兼容原始ReAct格式)
        search_match = re.search(r'Search\[([^\]]+)\]', action_text)
        if search_match:
            return ActionType.SEARCH, search_match.group(1)
        
        # Lookup[term] (兼容原始ReAct格式)
        lookup_match = re.search(r'Lookup\[([^\]]+)\]', action_text)
        if lookup_match:
            return ActionType.LOOKUP, lookup_match.group(1)
        
        return None, ""
    
    def _execute_action(self, step: ReActStep) -> str:
        """执行行动"""
        try:
            if step.action_type == ActionType.CALL_APP:
                return self._execute_app_call(step.action_input)
            elif step.action_type == ActionType.SEARCH:
                return self._execute_search(step.action_input)
            elif step.action_type == ActionType.LOOKUP:
                return self._execute_lookup(step.action_input)
            else:
                return "Unknown action type"
                
        except Exception as e:
            logger.error(f"Error executing action: {e}")
            return f"Error: {str(e)}"
    
    def _execute_app_call(self, action_input: str) -> str:
        """执行应用调用"""
        try:
            # 解析应用名称和参数
            parts = action_input.split('|', 1)
            app_name = parts[0]
            params_str = parts[1] if len(parts) > 1 else "{}"
            
            # 解析参数JSON
            try:
                params = json.loads(params_str)
            except json.JSONDecodeError:
                return f"Invalid parameters format: {params_str}"
            
            # 执行应用
            result = self.app_registry.execute_app(app_name, **params)
            
            if result['success']:
                return str(result['result'])
            else:
                return f"Error: {result['error']}"
                
        except Exception as e:
            return f"Error executing app call: {str(e)}"
    
    def _execute_search(self, query: str) -> str:
        """执行搜索（使用搜索应用）"""
        result = self.app_registry.execute_app('search', query=query)
        if result['success']:
            return str(result['result'])
        else:
            return f"Search failed: {result['error']}"
    
    def _execute_lookup(self, term: str) -> str:
        """执行查找（模拟实现）"""
        return f"Lookup result for '{term}': [相关信息]"
