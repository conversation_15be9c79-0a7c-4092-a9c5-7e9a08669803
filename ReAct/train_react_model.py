#!/usr/bin/env python3
"""
ReAct模型训练脚本
基于LLaMA-Factory框架训练ReAct应用调用模型
"""

import os
import json
import yaml
import argparse
import logging
from pathlib import Path

from src.app_registry import AppRegistry
from src.data_generator import DataGenerator

logger = logging.getLogger(__name__)

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def load_config(config_path: str) -> dict:
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

def generate_training_data(config: dict, output_dir: str):
    """生成训练数据"""
    logger.info("Generating training data...")
    
    # 初始化应用注册表
    app_registry = AppRegistry()
    app_registry.load_config('app_caller_config.yaml')
    
    # 构建系统提示
    app_descriptions = []
    for app_def in app_registry.get_app_definitions():
        params_desc = []
        for param in app_def['parameters']:
            param_str = f"{param['name']} ({param['type']})"
            if param['required']:
                param_str += " [必需]"
            param_str += f": {param['description']}"
            params_desc.append(param_str)
        
        app_desc = f"- {app_def['name']}: {app_def['description']}\n  参数: {', '.join(params_desc)}"
        app_descriptions.append(app_desc)
    
    apps_text = "\n".join(app_descriptions)
    
    system_prompt = f"""你是一个智能助手，能够理解用户的需求并调用相应的应用程序来完成任务。

可用的应用程序:
{apps_text}

你需要使用ReAct (Reasoning and Acting) 方法来解决问题：
1. 思考 (Thought): 分析用户需求，确定需要采取的行动
2. 行动 (Action): 选择合适的应用程序并提供参数
3. 观察 (Observation): 查看应用程序的执行结果
4. 重复上述过程直到完成任务

行动格式:
- CallApp[应用名称]: 调用指定的应用程序
  参数格式: {{"param1": "value1", "param2": "value2"}}
- Finish[最终答案]: 完成任务并给出最终答案

现在开始处理用户的请求。记住要逐步思考，选择合适的应用程序，并根据结果给出最终答案。"""
    
    # 生成训练数据
    data_generator = DataGenerator(app_registry, system_prompt)
    
    # 生成数据集
    training_config = config.get('training', {})
    single_app_examples = training_config.get('single_app_examples', 800)
    multi_app_examples = training_config.get('multi_app_examples', 200)
    
    all_examples = data_generator.generate_dataset(single_app_examples, multi_app_examples)
    
    # 分割数据集
    train_examples, val_examples, test_examples = data_generator.split_dataset(all_examples)
    
    # 保存数据集
    os.makedirs(output_dir, exist_ok=True)
    
    data_generator.save_dataset(train_examples, os.path.join(output_dir, 'react_train.jsonl'))
    data_generator.save_dataset(val_examples, os.path.join(output_dir, 'react_validation.jsonl'))
    data_generator.save_dataset(test_examples, os.path.join(output_dir, 'react_test.jsonl'))
    
    logger.info(f"Training data generated: {len(train_examples)} train, {len(val_examples)} val, {len(test_examples)} test")

def create_llamafactory_config(config: dict, data_dir: str, output_dir: str) -> dict:
    """创建LLaMA-Factory训练配置"""
    training_config = config.get('training', {})
    fine_tuning_config = training_config.get('fine_tuning', {})
    
    llamafactory_config = {
        "stage": "sft",
        "do_train": True,
        "model_name_or_path": config['model']['model_path'],
        "dataset": "react_app_caller",
        "template": "qwen",
        "finetuning_type": "lora",
        "output_dir": output_dir,
        
        # 数据配置
        "overwrite_cache": True,
        "overwrite_output_dir": True,
        "cutoff_len": config['model'].get('max_length', 2048),
        "preprocessing_num_workers": 16,
        
        # 训练参数
        "per_device_train_batch_size": fine_tuning_config.get('batch_size', 4),
        "per_device_eval_batch_size": 1,
        "gradient_accumulation_steps": fine_tuning_config.get('gradient_accumulation_steps', 4),
        "learning_rate": fine_tuning_config.get('learning_rate', 2e-5),
        "num_train_epochs": fine_tuning_config.get('num_epochs', 3),
        "lr_scheduler_type": fine_tuning_config.get('scheduler', 'cosine'),
        "warmup_ratio": fine_tuning_config.get('warmup_ratio', 0.1),
        "weight_decay": fine_tuning_config.get('weight_decay', 0.01),
        "max_grad_norm": fine_tuning_config.get('max_grad_norm', 1.0),
        
        # LoRA配置
        "lora_r": fine_tuning_config.get('lora', {}).get('r', 16),
        "lora_alpha": fine_tuning_config.get('lora', {}).get('alpha', 32),
        "lora_dropout": fine_tuning_config.get('lora', {}).get('dropout', 0.1),
        "lora_target": ",".join(fine_tuning_config.get('lora', {}).get('target_modules', ["q_proj", "v_proj"])),
        
        # 优化配置
        "fp16": fine_tuning_config.get('fp16', True),
        "gradient_checkpointing": fine_tuning_config.get('gradient_checkpointing', True),
        
        # 日志和保存
        "logging_steps": 10,
        "save_steps": 100,
        "eval_steps": 100,
        "evaluation_strategy": "steps",
        "save_strategy": "steps",
        "load_best_model_at_end": True,
        "metric_for_best_model": "eval_loss",
        "greater_is_better": False,
    }
    
    return llamafactory_config

def create_dataset_info(data_dir: str) -> dict:
    """创建数据集信息文件"""
    dataset_info = {
        "react_app_caller": {
            "file_name": "react_train.jsonl",
            "formatting": "sharegpt",
            "columns": {
                "messages": "messages"
            },
            "tags": {
                "role_tag": "role",
                "content_tag": "content",
                "user_tag": "user",
                "assistant_tag": "assistant",
                "system_tag": "system"
            }
        }
    }
    
    return dataset_info

def main():
    parser = argparse.ArgumentParser(description='Train ReAct App Caller Model')
    parser.add_argument('--config', type=str, default='app_caller_config.yaml',
                       help='Configuration file path')
    parser.add_argument('--data-dir', type=str, default='training_data',
                       help='Training data directory')
    parser.add_argument('--output-dir', type=str, default='react_model_output',
                       help='Model output directory')
    parser.add_argument('--generate-data-only', action='store_true',
                       help='Only generate training data, do not train')
    parser.add_argument('--llamafactory-path', type=str, default='~/LLaMA-Factory',
                       help='LLaMA-Factory installation path')
    
    args = parser.parse_args()
    
    setup_logging()
    
    # 加载配置
    config = load_config(args.config)
    
    # 生成训练数据
    generate_training_data(config, args.data_dir)
    
    if args.generate_data_only:
        logger.info("Data generation completed. Exiting.")
        return
    
    # 创建LLaMA-Factory配置
    llamafactory_config = create_llamafactory_config(config, args.data_dir, args.output_dir)
    
    # 保存训练配置
    config_file = os.path.join(args.data_dir, 'training_config.json')
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(llamafactory_config, f, indent=2, ensure_ascii=False)
    
    # 创建数据集信息文件
    dataset_info = create_dataset_info(args.data_dir)
    dataset_info_file = os.path.join(args.data_dir, 'dataset_info.json')
    with open(dataset_info_file, 'w', encoding='utf-8') as f:
        json.dump(dataset_info, f, indent=2, ensure_ascii=False)
    
    logger.info(f"Training configuration saved to {config_file}")
    logger.info(f"Dataset info saved to {dataset_info_file}")
    
    # 生成训练命令
    llamafactory_path = os.path.expanduser(args.llamafactory_path)
    train_command = f"""
# 训练ReAct应用调用模型
cd {llamafactory_path}

# 复制数据集文件
cp {os.path.abspath(args.data_dir)}/react_train.jsonl data/
cp {os.path.abspath(args.data_dir)}/react_validation.jsonl data/
cp {os.path.abspath(args.data_dir)}/dataset_info.json data/

# 开始训练
llamafactory-cli train \\
    --stage sft \\
    --do_train \\
    --model_name_or_path {config['model']['model_path']} \\
    --dataset react_app_caller \\
    --template qwen \\
    --finetuning_type lora \\
    --output_dir {args.output_dir} \\
    --per_device_train_batch_size {llamafactory_config['per_device_train_batch_size']} \\
    --gradient_accumulation_steps {llamafactory_config['gradient_accumulation_steps']} \\
    --learning_rate {llamafactory_config['learning_rate']} \\
    --num_train_epochs {llamafactory_config['num_train_epochs']} \\
    --lr_scheduler_type {llamafactory_config['lr_scheduler_type']} \\
    --warmup_ratio {llamafactory_config['warmup_ratio']} \\
    --fp16 \\
    --gradient_checkpointing \\
    --logging_steps {llamafactory_config['logging_steps']} \\
    --save_steps {llamafactory_config['save_steps']} \\
    --eval_steps {llamafactory_config['eval_steps']} \\
    --evaluation_strategy steps \\
    --load_best_model_at_end \\
    --overwrite_output_dir
"""
    
    # 保存训练命令
    train_script = os.path.join(args.data_dir, 'train_react_model.sh')
    with open(train_script, 'w') as f:
        f.write(train_command)
    
    os.chmod(train_script, 0o755)
    
    logger.info(f"Training script saved to {train_script}")
    logger.info("Run the training script to start training the ReAct model")

if __name__ == "__main__":
    main()
