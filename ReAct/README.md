# ReAct应用调用系统

基于ReAct (Reasoning and Acting) 方法的智能应用调用系统，能够理解用户需求并自动调用相关应用程序完成任务。

## 🌟 特性

- **智能推理**: 使用ReAct方法进行逐步推理和行动
- **应用管理**: 支持内置和外部应用程序的注册和管理
- **模型微调**: 基于LLaMA-Factory框架进行模型微调
- **评估系统**: 完整的评估框架，支持多种指标
- **易于扩展**: 模块化设计，易于添加新的应用程序

## 🏗️ 系统架构

```
ReAct应用调用系统
├── 应用注册表 (AppRegistry)
│   ├── 内置应用 (计算器、天气、搜索等)
│   └── 外部应用 (API调用)
├── ReAct推理引擎 (ReActEngine)
│   ├── 意图识别
│   ├── 应用选择
│   └── 参数提取
├── 模型接口 (ModelInterface)
│   ├── 本地模型 (Qwen等)
│   └── API模型 (OpenAI等)
└── 评估系统 (Evaluator)
    ├── 性能指标
    └── 错误分析
```

## 📦 安装和设置

### 1. 环境准备

```bash
# 克隆项目
cd ReAct

# 安装依赖
pip install torch transformers datasets accelerate peft bitsandbytes
pip install pyyaml

# 如果需要微调，确保LLaMA-Factory已安装
# 参考: https://github.com/hiyouga/LLaMA-Factory
```

### 2. 配置文件

编辑 `app_caller_config.yaml` 配置文件：

```yaml
# 模型配置
model:
  model_path: "~/qwen-finetune/models/Qwen/Qwen2.5-3B-Instruct"
  lora_path: null  # 微调后的LoRA权重路径

# 应用配置
applications:
  builtin_apps:
    - name: "calculator"
      description: "执行数学计算"
      # ... 更多配置
```

## 🚀 快速开始

### 1. 创建测试用例

```bash
python demo_react_app_caller.py --mode create-test
```

### 2. 交互式演示

```bash
python demo_react_app_caller.py --mode interactive
```

### 3. 批量测试

```bash
python demo_react_app_caller.py --mode batch
```

### 4. 模型评估

```bash
python demo_react_app_caller.py --mode eval --test-cases test_cases.json
```

## 🔧 模型训练

### 1. 生成训练数据

```bash
python train_react_model.py --generate-data-only
```

### 2. 开始训练

```bash
python train_react_model.py --config app_caller_config.yaml
```

训练脚本会自动：
- 生成ReAct格式的训练数据
- 创建LLaMA-Factory兼容的配置
- 生成训练命令脚本

### 3. 使用训练好的模型

更新配置文件中的 `lora_path` 指向训练好的LoRA权重：

```yaml
model:
  lora_path: "path/to/your/lora/weights"
```

## 📊 评估指标

系统支持以下评估指标：

- **意图识别准确率**: 正确理解用户意图的比例
- **应用选择准确率**: 正确选择应用程序的比例
- **参数提取F1分数**: 参数提取的准确性
- **端到端成功率**: 完整任务完成的成功率
- **推理质量评分**: 推理过程的质量评估

## 🔌 添加新应用

### 1. 内置应用

在 `app_caller_config.yaml` 中添加应用定义：

```yaml
applications:
  builtin_apps:
    - name: "my_app"
      description: "我的应用描述"
      function: "my_function"
      parameters:
        - name: "param1"
          type: "string"
          description: "参数描述"
          required: true
```

在 `app_registry.py` 中实现对应函数：

```python
def _my_function(self, param1: str) -> str:
    # 实现应用逻辑
    return "应用执行结果"
```

### 2. 外部应用

创建 `external_apps.json` 文件：

```json
[
  {
    "name": "external_app",
    "description": "外部应用",
    "function": "api_endpoint",
    "parameters": [...],
    "api_config": {
      "url": "https://api.example.com/endpoint",
      "method": "POST"
    }
  }
]
```

## 📝 使用示例

```python
from src.app_caller import ReActAppCaller

# 初始化系统
app_caller = ReActAppCaller('app_caller_config.yaml')

# 处理用户查询
result = app_caller.process_query("帮我计算 25 * 4")

if result.success:
    print(f"最终答案: {result.final_answer}")

    # 查看推理过程
    for step in result.steps:
        print(f"思考: {step.thought}")
        print(f"行动: {step.action}")
        print(f"观察: {step.observation}")
else:
    print(f"处理失败: {result.error_message}")
```

## 🎯 支持的应用类型

目前系统内置支持以下应用类型：

- **计算器**: 数学表达式计算
- **天气查询**: 获取天气信息
- **搜索引擎**: 信息搜索
- **翻译器**: 文本翻译
- **邮件发送**: 发送邮件
- **日程管理**: 管理日程安排

## 🔍 故障排除

### 常见问题

1. **模型加载失败**
   - 检查模型路径是否正确
   - 确保有足够的GPU内存

2. **应用调用失败**
   - 检查应用配置是否正确
   - 查看日志文件获取详细错误信息

3. **训练数据生成失败**
   - 确保配置文件格式正确
   - 检查应用注册表是否正常加载

### 日志配置

在配置文件中设置日志级别：

```yaml
logging:
  level: "DEBUG"  # INFO, WARNING, ERROR
  file: "react_app_caller.log"
```

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

本项目基于原始ReAct项目的许可证。

## 🙏 致谢

- 原始ReAct论文: [ReAct: Synergizing Reasoning and Acting in Language Models](https://arxiv.org/abs/2210.03629)
- LLaMA-Factory框架: https://github.com/hiyouga/LLaMA-Factory
