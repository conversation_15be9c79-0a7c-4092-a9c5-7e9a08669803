#!/usr/bin/env python3
"""
ReAct应用调用系统演示脚本
"""

import argparse
import json
import logging
from pathlib import Path

from src.app_caller import ReActAppCaller
from src.evaluator import ReActEvaluator, load_test_cases

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def interactive_demo(app_caller: ReActAppCaller):
    """交互式演示"""
    print("=== ReAct应用调用系统演示 ===")
    print("可用的应用程序:")
    
    for app_def in app_caller.get_app_definitions():
        print(f"- {app_def['name']}: {app_def['description']}")
    
    print("\n输入 'quit' 退出演示")
    print("-" * 50)
    
    while True:
        try:
            user_input = input("\n请输入您的需求: ").strip()
            
            if user_input.lower() in ['quit', 'exit', '退出']:
                print("感谢使用！")
                break
            
            if not user_input:
                continue
            
            print("\n🤔 正在思考和处理...")
            result = app_caller.process_query(user_input)
            
            if result.success:
                print(f"\n✅ 处理成功！")
                print(f"最终答案: {result.final_answer}")
                
                print(f"\n📝 推理过程:")
                for i, step in enumerate(result.steps, 1):
                    print(f"步骤 {i}:")
                    print(f"  思考: {step.thought}")
                    print(f"  行动: {step.action}")
                    if step.observation:
                        print(f"  观察: {step.observation}")
                    print()
            else:
                print(f"\n❌ 处理失败: {result.error_message}")
                
        except KeyboardInterrupt:
            print("\n\n感谢使用！")
            break
        except Exception as e:
            print(f"\n❌ 发生错误: {e}")

def batch_test(app_caller: ReActAppCaller, test_queries: list):
    """批量测试"""
    print("=== 批量测试 ===")
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n测试 {i}: {query}")
        print("-" * 40)
        
        result = app_caller.process_query(query)
        
        if result.success:
            print(f"✅ 成功: {result.final_answer}")
        else:
            print(f"❌ 失败: {result.error_message}")

def evaluation_demo(app_caller: ReActAppCaller, test_cases_file: str):
    """评估演示"""
    print("=== 模型评估演示 ===")
    
    # 加载测试用例
    test_cases = load_test_cases(test_cases_file)
    if not test_cases:
        print("没有找到测试用例文件或文件为空")
        return
    
    # 创建评估器
    evaluator = ReActEvaluator(app_caller)
    
    # 运行评估
    print(f"正在评估 {len(test_cases)} 个测试用例...")
    metrics = evaluator.evaluate(test_cases)
    
    # 显示结果
    print("\n📊 评估结果:")
    print(f"意图识别准确率: {metrics.intent_accuracy:.2%}")
    print(f"应用选择准确率: {metrics.app_selection_accuracy:.2%}")
    print(f"参数提取F1分数: {metrics.parameter_extraction_f1:.2%}")
    print(f"端到端成功率: {metrics.end_to_end_success_rate:.2%}")
    print(f"推理质量评分: {metrics.reasoning_quality:.2f}")
    
    # 生成详细报告
    report = evaluator.generate_detailed_report(test_cases, "evaluation_report.json")
    print(f"\n详细报告已保存到: evaluation_report.json")

def create_sample_test_cases():
    """创建示例测试用例"""
    test_cases = [
        {
            "id": "calc_1",
            "user_query": "帮我计算 25 * 4",
            "expected_app": "calculator",
            "expected_parameters": {"expression": "25 * 4"},
            "expected_result": "计算结果: 100",
            "category": "calculation"
        },
        {
            "id": "weather_1",
            "user_query": "北京今天天气怎么样",
            "expected_app": "weather",
            "expected_parameters": {"location": "北京"},
            "expected_result": "北京的天气: 晴天，温度25°C",
            "category": "weather"
        },
        {
            "id": "search_1",
            "user_query": "搜索人工智能的发展历史",
            "expected_app": "search",
            "expected_parameters": {"query": "人工智能发展历史"},
            "expected_result": "搜索'人工智能发展历史'的结果: 找到5条相关信息",
            "category": "search"
        },
        {
            "id": "translate_1",
            "user_query": "把'Hello World'翻译成中文",
            "expected_app": "translator",
            "expected_parameters": {"text": "Hello World", "target_language": "中文"},
            "expected_result": "将'Hello World'翻译为中文: [翻译结果]",
            "category": "translation"
        },
        {
            "id": "email_1",
            "user_query": "发邮件给*****************，主题是会议通知",
            "expected_app": "email",
            "expected_parameters": {
                "recipient": "<EMAIL>", 
                "subject": "会议通知",
                "content": "关于会议通知的邮件内容"
            },
            "expected_result": "邮件已发送给*****************，主题: 会议通知",
            "category": "email"
        }
    ]
    
    with open("test_cases.json", "w", encoding="utf-8") as f:
        json.dump(test_cases, f, indent=2, ensure_ascii=False)
    
    print("示例测试用例已创建: test_cases.json")

def main():
    parser = argparse.ArgumentParser(description='ReAct App Caller Demo')
    parser.add_argument('--config', type=str, default='app_caller_config.yaml',
                       help='Configuration file path')
    parser.add_argument('--mode', type=str, choices=['interactive', 'batch', 'eval', 'create-test'],
                       default='interactive', help='Demo mode')
    parser.add_argument('--test-cases', type=str, default='test_cases.json',
                       help='Test cases file for evaluation')
    
    args = parser.parse_args()
    
    setup_logging()
    
    if args.mode == 'create-test':
        create_sample_test_cases()
        return
    
    # 初始化系统
    print("正在初始化ReAct应用调用系统...")
    try:
        app_caller = ReActAppCaller(args.config)
        
        if not app_caller.is_ready():
            print("❌ 系统未就绪，请检查配置")
            return
        
        print("✅ 系统初始化完成")
        
    except Exception as e:
        print(f"❌ 系统初始化失败: {e}")
        return
    
    # 根据模式运行演示
    if args.mode == 'interactive':
        interactive_demo(app_caller)
    
    elif args.mode == 'batch':
        test_queries = [
            "计算 15 + 27",
            "查询上海的天气",
            "搜索机器学习教程",
            "翻译'Good morning'为中文",
            "发邮件给****************，主题是项目更新"
        ]
        batch_test(app_caller, test_queries)
    
    elif args.mode == 'eval':
        if Path(args.test_cases).exists():
            evaluation_demo(app_caller, args.test_cases)
        else:
            print(f"测试用例文件不存在: {args.test_cases}")
            print("使用 --mode create-test 创建示例测试用例")

if __name__ == "__main__":
    main()
