# 婴幼儿领域大模型评估框架使用指南

## 🎯 概述

这个评估框架专门为婴幼儿领域的大模型评估而设计，支持对Qwen等7B模型在9个不同方向数据集上的全面评估。框架已经过测试，基础功能运行正常。

## 📊 数据集信息

你的数据集包含以下9个方向：

| 数据集 | 文件路径 | 样本数量 | 格式 | 描述 |
|--------|----------|----------|------|------|
| phoneme | datasets/1-phoneme/enhanced_speech_assessment_instruction_format.jsonl | 1000 | JSONL | 语音构音评估 |
| scene | datasets/2-scene/cribhd_instruction_dataset.json | 9000+ | JSON | 场景监测 |
| meal | datasets/3-meal/infant_nutrition_alpaca_format_20250728_144636.json | 1000+ | JSON | 营养喂养 |
| body | datasets/4-body/consolidated_growth_monitoring_dataset.json | 746 | JSON | 生长发育 |
| pronunciation | datasets/5-pronunciation/final_training_data_all_data_20250728_151919.json | 1000+ | JSON | 发音训练 |
| gross_motor | datasets/6-gross_motor/consolidated_infant_motor_dataset.json | 500+ | JSON | 大运动发展 |
| fine_motor | datasets/7-fine-motor/comprehensive_instruction_dataset.jsonl | 1000+ | JSONL | 精细运动 |
| emotion | datasets/9-emotion/infant_emotion_instruction_tuning_dataset.json | 1000+ | JSON | 情绪发展 |
| language | datasets/10-language/unified_instruction_dataset.json | 538 | JSON | 语言发展 |

## 🚀 快速开始

### 1. 验证基础功能

```bash
# 运行简化测试（不需要深度学习库）
python3 scripts/simple_test.py

# 运行演示脚本
python3 simple_demo.py
```

### 2. 安装完整依赖

```bash
# 安装深度学习相关依赖
pip install torch transformers

# 安装评估指标依赖
pip install nltk rouge-score bert-score jieba

# 安装可视化依赖
pip install matplotlib seaborn plotly

# 或者一次性安装所有依赖
pip install -r requirements.txt
```

### 3. 配置模型

编辑 `config.yaml` 文件，配置你要评估的Qwen模型：

```yaml
models:
  qwen_7b_chat:
    model_name: "Qwen/Qwen-7B-Chat"  # 或你的本地模型路径
    model_type: "local"
    max_length: 2048
    temperature: 0.7
    top_p: 0.9
    
  qwen_14b_chat:
    model_name: "Qwen/Qwen-14B-Chat"
    model_type: "local"
    max_length: 2048
    temperature: 0.7
    top_p: 0.9
```

### 4. 运行评估

```bash
# 快速测试（每个数据集10个样本）
python3 run_evaluation.py --model qwen_7b_chat --sample-size 10

# 评估特定数据集
python3 run_evaluation.py --model qwen_7b_chat --dataset phoneme --sample-size 50

# 完整评估（所有数据集，每个100个样本）
python3 run_evaluation.py --model qwen_7b_chat --sample-size 100
```

## 📈 评估指标

框架提供多维度评估：

### 文本质量指标
- **BLEU**: 基于n-gram的文本相似度
- **ROUGE**: 基于召回率的文本质量评估  
- **BERTScore**: 基于语义相似度的评估

### 专业性指标
- **安全性评估**: 检测潜在的安全风险建议
- **专业准确性**: 评估专业术语使用和建议质量
- **结构化程度**: 评估回答的组织结构

### 综合指标
- **总体分数**: 各项指标的加权平均
- **成功率**: 成功生成回答的比例
- **推理时间**: 模型响应时间统计

## 📊 结果分析

### 查看评估结果

```bash
# 分析评估结果
python3 scripts/analyze_results.py evaluation_results/evaluation_results_qwen_7b_chat_*.json

# 生成可视化图表
python3 scripts/analyze_results.py results.json --plot

# 导出CSV报告
python3 scripts/analyze_results.py results.json --csv
```

### 结果文件结构

```
evaluation_results/
├── evaluation_results_qwen_7b_chat_20250728_143022.json  # 详细结果
├── evaluation_results_qwen_7b_chat_20250728_143022.csv   # 简化报告
└── analysis_plots/                                       # 可视化图表
    ├── qwen_7b_chat_overview.png
    ├── qwen_7b_chat_heatmap.png
    └── qwen_7b_chat_stats.png
```

## 🔧 高级配置

### 调整评估参数

在 `config.yaml` 中修改：

```yaml
evaluation:
  sample_size: 100        # 每个数据集的样本数量
  batch_size: 8          # 批处理大小
  metrics:               # 启用的评估指标
    - "bleu"
    - "rouge"
    - "bert_score"
    - "safety_score"
    - "professional_accuracy"

# 调整指标权重
metric_weights:
  bleu: 0.2
  rouge_l: 0.2
  bert_score: 0.2
  safety_score: 0.2
  professional_accuracy: 0.2
```

### 使用API模型

如果你想使用API调用而不是本地模型：

```yaml
models:
  qwen_api:
    model_name: "qwen-plus"
    model_type: "api"
    api_url: "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
    api_key: "your-api-key"
    max_new_tokens: 512
    temperature: 0.7
```

## 💡 使用建议

### 1. 渐进式评估

```bash
# 第一步：快速验证（每个数据集5个样本）
python3 run_evaluation.py --model qwen_7b_chat --sample-size 5

# 第二步：中等规模测试（每个数据集50个样本）
python3 run_evaluation.py --model qwen_7b_chat --sample-size 50

# 第三步：完整评估（每个数据集100个样本）
python3 run_evaluation.py --model qwen_7b_chat --sample-size 100
```

### 2. 针对性评估

```bash
# 只评估语音相关数据集
python3 run_evaluation.py --model qwen_7b_chat --dataset phoneme

# 只评估运动发展数据集
python3 run_evaluation.py --model qwen_7b_chat --dataset gross_motor
```

### 3. 批量评估多个模型

创建批量评估脚本：

```bash
#!/bin/bash
models=("qwen_7b_chat" "qwen_14b_chat")
for model in "${models[@]}"; do
    echo "评估模型: $model"
    python3 run_evaluation.py --model $model --sample-size 50
done
```

## ⚠️ 注意事项

### 硬件要求
- **GPU内存**: 7B模型建议16GB+显存，14B模型建议24GB+显存
- **系统内存**: 建议32GB+系统内存
- **存储空间**: 模型文件和结果文件需要足够存储空间

### 性能优化
- 使用较小的batch_size避免内存溢出
- 启用fp16精度加速推理
- 使用gradient_checkpointing节省内存

### 结果解释
- 评估结果仅供参考，实际应用需结合专业判断
- 不同数据集的难度不同，分数不能直接比较
- 安全性评估基于关键词检测，可能有误报

## 🛠️ 故障排除

### 常见问题

1. **模型加载失败**
   ```bash
   # 检查模型路径是否正确
   # 确保有足够的GPU内存
   # 尝试使用CPU模式（速度较慢）
   ```

2. **数据加载错误**
   ```bash
   # 运行简化测试检查数据完整性
   python3 scripts/simple_test.py
   ```

3. **依赖包缺失**
   ```bash
   # 重新安装依赖
   pip install -r requirements.txt
   ```

### 获取帮助

```bash
# 查看命令行帮助
python3 run_evaluation.py --help

# 查看配置文件示例
cat config.yaml
```

## 📞 技术支持

如果遇到问题：

1. 首先运行 `python3 scripts/simple_test.py` 检查基础功能
2. 检查 `config.yaml` 配置是否正确
3. 确认所有依赖包已正确安装
4. 查看错误日志获取详细信息

## 🎉 开始评估

现在你可以开始评估你的Qwen模型了！建议从小规模测试开始：

```bash
# 快速测试
python3 run_evaluation.py --model qwen_7b_chat --sample-size 10

# 查看结果
python3 scripts/analyze_results.py evaluation_results/evaluation_results_*.json --plot
```

祝你评估顺利！🚀
