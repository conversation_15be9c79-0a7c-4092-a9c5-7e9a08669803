# 大模型评估框架依赖
torch>=2.0.0
transformers>=4.30.0
datasets>=2.12.0
accelerate>=0.20.0
sentencepiece>=0.1.99
protobuf>=3.20.0

# 评估指标
nltk>=3.8
rouge-score>=0.1.2
sacrebleu>=2.3.1
bert-score>=0.3.13

# 数据处理
pandas>=2.0.0
numpy>=1.24.0
jsonlines>=3.1.0
tqdm>=4.65.0

# 可视化和报告
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.14.0

# API调用（如果使用在线模型）
openai>=0.27.0
requests>=2.31.0

# 配置管理
pyyaml>=6.0
hydra-core>=1.3.0

# 日志和监控
wandb>=0.15.0
tensorboard>=2.13.0

# 工具类
rich>=13.0.0
click>=8.1.0
