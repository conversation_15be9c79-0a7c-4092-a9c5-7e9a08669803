---
language: zh
license: apache-2.0
dataset_id: cribhd-infant-monitoring-text
tags:
- infant-monitoring
- scene-description
- child-development
- safety-assessment
- multimodal
- vision-language
- chinese
- healthcare
- developmental-milestones
task_categories:
- text-generation
- text-classification
- question-answering
size_categories:
- 1K<n<10K
---

# CribHD婴幼儿家庭场景运动监测文本数据集

## 数据集概述

**CribHD-Infant-Monitoring-Text** 是一个专门用于婴幼儿家庭场景运动监测的中文文本数据集。该数据集基于CribHD图像数据集，通过多模态AI模型（Qwen-VL）生成了详细的场景描述、发育评估和安全风险分析，为婴幼儿监护、发育评估和安全预警等应用提供高质量的训练数据。

### 🎯 主要特点

- **专业性强**: 基于国家卫健委发育里程碑和WHO标准进行评估
- **结构化描述**: 包含5个维度的专业分析（场景、发育、安全、指导、总结）
- **多场景覆盖**: 涵盖毯子使用、玩具互动和危机场景三大类别
- **高质量标注**: 使用Qwen多模态模型生成，经过质量控制
- **即用格式**: 提供训练就绪的数据分割和多种导出格式

### 📊 数据集统计

| 指标 | 数值 |
|------|------|
| **总样本数** | 9,638 |
| **数据格式** | 指令-输入-输出三元组 |
| **文件大小** | 9.4MB |
| **语言** | 中文 |
| **生成模型** | Qwen-VL-Plus |
| **数据来源** | CribHD数据集 |

### 🏷️ 数据格式

本数据集采用标准的**指令-输入-输出三元组**格式，适用于大模型指令微调训练：

- **instruction**: 任务指令，描述需要完成的评估任务
- **input**: 输入信息，包含场景信息、婴儿年龄、发育水平等
- **output**: 期望输出，包含专业的场景分析和发育评估结果

## 数据集结构

```
2-scene/
├── README.md                           # 本文档
└── cribhd_instruction_dataset.json    # 指令微调数据集 (9,638样本)
```

## 数据格式

### 指令-输入-输出格式 (cribhd_instruction_dataset.json)

```json
{
  "metadata": {
    "source": "CribHD婴幼儿家庭场景运动监测文本数据集",
    "conversion_time": "2025-07-27T19:20:07.940527",
    "format": "instruction-input-output",
    "original_samples": 1614,
    "instruction_samples": 9638
  },
  "data": [
    {
      "instruction": "请提供这张婴儿床场景的综合分析报告，包括发育评估、安全分析和指导建议。",
      "input": "场景信息: 婴儿年龄: 6个月, 场景类型: crib, 发育水平: 正常, 图像: train10.jpg",
      "output": "## 1. 基础场景描述\n- 婴儿年龄估计：6-12个月\n- 婴儿当前姿势和动作状态：婴儿正在睡觉，侧卧在婴儿床上，双手紧握着被子的一角。\n- 婴儿床内物品清单：婴儿床、蓝色毯子\n\n## 2. 运动发育评估\n- 大运动技能观察：婴儿能够侧卧并保持稳定，说明其翻身能力较强。但无法判断其抬头、坐立、爬行和站立的能力。\n- 精细动作技能观察：婴儿能够紧握被子的一角，说明其抓握能力较好。\n- 发育水平评估：根据大运动技能和精细动作技能的观察，该婴儿的发育水平正常。\n- 与该年龄段发育里程碑的对比：6-12个月的婴儿通常能够翻身、探索周围环境、尝试坐立和爬行。\n\n## 3. 安全风险评估\n- 玩具安全性分析：无玩具存在。\n- 毯子使用安全性：婴儿床内只有一条毯子，没有覆盖面部或限制活动的风险。\n- 其他潜在风险因素：无其他明显安全风险。\n- 风险等级评定：低风险\n\n## 4. 育儿指导建议\n- 针对当前发育阶段的活动建议：继续鼓励婴儿进行翻身练习，增加其探索周围环境的机会。可以提供一些安全的玩具供婴儿抓握和探索。\n- 安全改进措施：确保婴儿床的安全性，避免任何可能造成窒息或伤害的物品靠近婴儿。\n- 家长注意事项：注意观察婴儿的睡眠姿势，确保其不会因过度包裹而影响呼吸。\n- 促进发育的互动方式：通过语言交流、唱歌和讲故事等方式，增强婴儿的语言能力和社交技能。\n\n## 5. 专业总结\n- 整体发育状况评价：该婴儿的发育状况正常，各项运动技能发展良好。\n- 重点关注事项：继续关注婴儿的翻身能力，并提供适当的刺激以促进其精细动作的发展。\n- 下一阶段发育预期：预计婴儿将在接下来的几个月内学会坐立、爬行和站立，同时继续发展其精细动作技能。"
    }
  ]
}
```

## 使用方法

### 🚀 快速开始

#### 使用Hugging Face Datasets

```python
from datasets import load_dataset

# 加载指令微调数据集
dataset = load_dataset('json', data_files='cribhd_instruction_dataset.json')

# 查看数据集信息
print(dataset)
print(f"总样本数: {len(dataset['train'])}")
print("示例数据:")
print(dataset['train'][0])
```

#### 直接加载JSON文件

```python
import json

# 加载指令微调数据集
with open('cribhd_instruction_dataset.json', 'r', encoding='utf-8') as f:
    dataset = json.load(f)

# 查看数据结构
print(f"元数据: {dataset['metadata']}")
print(f"数据样本数: {len(dataset['data'])}")
print("示例数据:")
print(dataset['data'][0])
```

### 📋 数据字段说明

#### 指令微调格式字段

| 字段名 | 类型 | 描述 |
|--------|------|------|
| `instruction` | string | 任务指令，描述需要完成的评估任务 |
| `input` | string | 输入信息，包含场景信息、婴儿年龄、发育水平等 |
| `output` | string | 期望输出，包含专业的场景分析和发育评估结果 |

#### 元数据字段

| 字段名 | 类型 | 描述 |
|--------|------|------|
| `source` | string | 数据集来源 |
| `conversion_time` | string | 数据转换时间 |
| `format` | string | 数据格式类型 |
| `original_samples` | int | 原始样本数量 |
| `instruction_samples` | int | 指令格式样本数量 |

### 🎯 应用场景

#### 1. 指令微调训练
```python
# 使用数据集进行指令微调训练
from transformers import AutoTokenizer, AutoModelForCausalLM, TrainingArguments, Trainer

# 加载数据
with open('cribhd_instruction_dataset.json', 'r', encoding='utf-8') as f:
    dataset = json.load(f)

# 数据预处理
def format_instruction(sample):
    return f"### 指令:\n{sample['instruction']}\n\n### 输入:\n{sample['input']}\n\n### 输出:\n{sample['output']}"

# 准备训练数据
train_texts = [format_instruction(sample) for sample in dataset['data']]
```

#### 2. 场景分析推理
```python
# 使用训练好的模型进行场景分析
def analyze_scene(instruction, input_info, model, tokenizer):
    prompt = f"### 指令:\n{instruction}\n\n### 输入:\n{input_info}\n\n### 输出:\n"
    inputs = tokenizer(prompt, return_tensors="pt")
    outputs = model.generate(**inputs, max_length=512, temperature=0.7)
    return tokenizer.decode(outputs[0], skip_special_tokens=True)

# 示例使用
instruction = "请提供这张婴儿床场景的综合分析报告，包括发育评估、安全分析和指导建议。"
input_info = "场景信息: 婴儿年龄: 8个月, 场景类型: crib, 发育水平: 正常"
result = analyze_scene(instruction, input_info, model, tokenizer)
```

## 数据质量

### 🔍 质量控制措施

1. **模型一致性**: 全部使用Qwen-VL-Plus模型生成，确保描述风格一致
2. **结构化模板**: 采用5段式专业评估模板，保证内容完整性
3. **专业标准**: 基于国家卫健委发育里程碑和WHO标准
4. **多维度评估**: 涵盖场景、发育、安全、指导、总结五个维度



### ⚠️ 已知限制

1. **数据来源**: 基于CribHD图像数据集生成，受原始数据集限制
2. **语言限制**: 仅支持中文描述
3. **AI生成内容**: 依赖Qwen-VL模型生成，可能存在一定偏差
4. **场景局限**: 主要覆盖家庭环境中的婴儿床场景

## 引用

如果您在研究中使用了此数据集，请引用：

```bibtex
@dataset{cribhd_infant_monitoring_text_2025,
  title={CribHD婴幼儿家庭场景运动监测文本数据集},
  author={Your Name},
  year={2025},
  publisher={Your Institution},
  version={2.0},
  description={基于CribHD图像数据集生成的婴幼儿监护文本数据集},
  url={https://your-dataset-url}
}
```

## 许可证

本数据集采用 **Apache 2.0** 许可证发布。

### 使用条款
- ✅ 允许商业使用
- ✅ 允许修改和分发
- ✅ 允许私人使用
- ⚠️ 需要保留版权声明
- ⚠️ 需要声明修改内容

### 免责声明
本数据集仅供研究和教育用途。生成的文本描述和评估建议不能替代专业医疗诊断，使用者应当谨慎使用并承担相应责任。

## 贡献与反馈

### 🤝 如何贡献
- 报告数据质量问题
- 提供改进建议
- 分享使用案例
- 贡献新的评估维度

### 📧 联系方式
- 邮箱: <EMAIL>
- GitHub Issues: [项目地址]
- 技术支持: [支持渠道]

## 更新日志

### v2.0 (2025-07-28)
- 🎉 数据格式升级为指令-输入-输出三元组
- 📊 扩展至9,638个高质量样本
- 🔧 适配大模型指令微调训练
- 📚 更新文档和使用示例

### v1.0 (2025-07-20)
- 🎉 初始版本发布
- 📊 包含1,499个高质量样本
- 🔧 支持多种数据格式
- 📚 完整的文档和使用示例

---

**致谢**: 感谢CribHD原始数据集的提供者、Qwen团队提供的多模态模型支持，以及所有为婴幼儿安全监护技术发展做出贡献的研究者们。