#!/usr/bin/env python3
"""
Qwen模型微调脚本 - 婴幼儿营养喂养数据集
使用LoRA进行参数高效微调
"""

import os
import json
import torch
from dataclasses import dataclass, field
from typing import Optional, Dict, Any
import transformers
from transformers import (
    AutoTokenizer,
    AutoModelForCausalLM,
    TrainingArguments,
    Trainer,
    DataCollatorForSeq2Seq,
    HfArgumentParser
)
from datasets import Dataset
from peft import LoraConfig, get_peft_model, TaskType
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class ModelArguments:
    """模型相关参数"""
    model_name_or_path: str = field(
        default="Qwen/Qwen-7B-Chat",
        metadata={"help": "预训练模型路径"}
    )
    cache_dir: Optional[str] = field(
        default=None,
        metadata={"help": "模型缓存目录"}
    )
    use_fast_tokenizer: bool = field(
        default=True,
        metadata={"help": "是否使用快速tokenizer"}
    )
    model_revision: str = field(
        default="main",
        metadata={"help": "模型版本"}
    )
    use_auth_token: bool = field(
        default=False,
        metadata={"help": "是否使用HuggingFace认证token"}
    )


@dataclass
class DataArguments:
    """数据相关参数"""
    train_file: str = field(
        default="infant_nutrition_train.json",
        metadata={"help": "训练数据文件路径"}
    )
    validation_file: str = field(
        default="infant_nutrition_validation.json", 
        metadata={"help": "验证数据文件路径"}
    )
    max_train_samples: Optional[int] = field(
        default=None,
        metadata={"help": "最大训练样本数"}
    )
    max_eval_samples: Optional[int] = field(
        default=None,
        metadata={"help": "最大验证样本数"}
    )
    max_source_length: int = field(
        default=512,
        metadata={"help": "输入序列最大长度"}
    )
    max_target_length: int = field(
        default=1536,
        metadata={"help": "输出序列最大长度"}
    )
    ignore_pad_token_for_loss: bool = field(
        default=True,
        metadata={"help": "计算loss时是否忽略pad token"}
    )


@dataclass
class LoraArguments:
    """LoRA相关参数"""
    use_lora: bool = field(
        default=True,
        metadata={"help": "是否使用LoRA"}
    )
    lora_r: int = field(
        default=16,
        metadata={"help": "LoRA rank"}
    )
    lora_alpha: int = field(
        default=32,
        metadata={"help": "LoRA alpha"}
    )
    lora_dropout: float = field(
        default=0.1,
        metadata={"help": "LoRA dropout"}
    )
    lora_target_modules: str = field(
        default="q_proj,v_proj,k_proj,o_proj",
        metadata={"help": "LoRA目标模块，逗号分隔"}
    )


def load_dataset_from_json(file_path: str, max_samples: Optional[int] = None) -> Dataset:
    """从JSON文件加载数据集"""
    logger.info(f"加载数据集: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    if max_samples:
        data = data[:max_samples]
    
    logger.info(f"加载了 {len(data)} 个样本")
    return Dataset.from_list(data)


def format_instruction(sample: Dict[str, Any]) -> str:
    """格式化指令"""
    instruction = sample['instruction']
    input_text = sample.get('input', '')
    
    if input_text.strip():
        prompt = f"### 指令:\n{instruction}\n\n### 输入:\n{input_text}\n\n### 输出:\n"
    else:
        prompt = f"### 指令:\n{instruction}\n\n### 输出:\n"
    
    return prompt


def preprocess_function(examples, tokenizer, data_args):
    """数据预处理函数"""
    inputs = []
    targets = []
    
    for i in range(len(examples['instruction'])):
        # 构建输入
        prompt = format_instruction({
            'instruction': examples['instruction'][i],
            'input': examples['input'][i]
        })
        
        # 目标输出
        target = examples['output'][i]
        
        inputs.append(prompt)
        targets.append(target)
    
    # Tokenize输入
    model_inputs = tokenizer(
        inputs,
        max_length=data_args.max_source_length,
        truncation=True,
        padding=False
    )
    
    # Tokenize目标
    with tokenizer.as_target_tokenizer():
        labels = tokenizer(
            targets,
            max_length=data_args.max_target_length,
            truncation=True,
            padding=False
        )
    
    # 合并输入和输出
    for i in range(len(model_inputs['input_ids'])):
        # 完整序列 = 输入 + 输出
        full_input_ids = model_inputs['input_ids'][i] + labels['input_ids'][i] + [tokenizer.eos_token_id]
        full_attention_mask = model_inputs['attention_mask'][i] + labels['attention_mask'][i] + [1]
        
        # 标签：输入部分用-100忽略，输出部分用于计算loss
        label_ids = [-100] * len(model_inputs['input_ids'][i]) + labels['input_ids'][i] + [tokenizer.eos_token_id]
        
        model_inputs['input_ids'][i] = full_input_ids
        model_inputs['attention_mask'][i] = full_attention_mask
        model_inputs['labels'] = model_inputs.get('labels', [])
        if len(model_inputs['labels']) <= i:
            model_inputs['labels'].extend([None] * (i + 1 - len(model_inputs['labels'])))
        model_inputs['labels'][i] = label_ids
    
    return model_inputs


def main():
    # 解析参数
    parser = HfArgumentParser((ModelArguments, DataArguments, TrainingArguments, LoraArguments))
    model_args, data_args, training_args, lora_args = parser.parse_args_into_dataclasses()
    
    # 设置日志级别
    logging.basicConfig(
        format="%(asctime)s - %(levelname)s - %(name)s - %(message)s",
        datefmt="%m/%d/%Y %H:%M:%S",
        level=logging.INFO,
    )
    
    # 加载tokenizer
    logger.info("加载tokenizer...")
    tokenizer = AutoTokenizer.from_pretrained(
        model_args.model_name_or_path,
        cache_dir=model_args.cache_dir,
        use_fast=model_args.use_fast_tokenizer,
        revision=model_args.model_revision,
        use_auth_token=True if model_args.use_auth_token else None,
        trust_remote_code=True
    )
    
    # 设置pad token
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    # 加载模型
    logger.info("加载模型...")
    model = AutoModelForCausalLM.from_pretrained(
        model_args.model_name_or_path,
        cache_dir=model_args.cache_dir,
        revision=model_args.model_revision,
        use_auth_token=True if model_args.use_auth_token else None,
        torch_dtype=torch.float16,
        device_map="auto",
        trust_remote_code=True
    )
    
    # 配置LoRA
    if lora_args.use_lora:
        logger.info("配置LoRA...")
        target_modules = lora_args.lora_target_modules.split(',')
        lora_config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            r=lora_args.lora_r,
            lora_alpha=lora_args.lora_alpha,
            lora_dropout=lora_args.lora_dropout,
            target_modules=target_modules,
            bias="none"
        )
        model = get_peft_model(model, lora_config)
        model.print_trainable_parameters()
    
    # 加载数据集
    train_dataset = load_dataset_from_json(data_args.train_file, data_args.max_train_samples)
    eval_dataset = load_dataset_from_json(data_args.validation_file, data_args.max_eval_samples)
    
    # 预处理数据
    logger.info("预处理数据...")
    train_dataset = train_dataset.map(
        lambda examples: preprocess_function(examples, tokenizer, data_args),
        batched=True,
        remove_columns=train_dataset.column_names
    )
    
    eval_dataset = eval_dataset.map(
        lambda examples: preprocess_function(examples, tokenizer, data_args),
        batched=True,
        remove_columns=eval_dataset.column_names
    )
    
    # 数据整理器
    data_collator = DataCollatorForSeq2Seq(
        tokenizer=tokenizer,
        model=model,
        label_pad_token_id=-100 if data_args.ignore_pad_token_for_loss else tokenizer.pad_token_id,
        pad_to_multiple_of=8
    )
    
    # 训练器
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=train_dataset,
        eval_dataset=eval_dataset,
        tokenizer=tokenizer,
        data_collator=data_collator,
    )
    
    # 开始训练
    logger.info("开始训练...")
    trainer.train()
    
    # 保存模型
    logger.info("保存模型...")
    trainer.save_model()
    tokenizer.save_pretrained(training_args.output_dir)
    
    logger.info("训练完成！")


if __name__ == "__main__":
    main()
