#!/bin/bash

# 自动找到空闲GPU并启动训练的脚本

echo "🔍 正在扫描空闲GPU..."

# 运行GPU查找脚本
python3 quick_gpu_finder.py

echo ""
echo "请根据上面的推荐选择GPU配置，然后运行相应的命令。"
echo ""
echo "或者使用以下交互式选择："

# 获取空闲GPU列表
FREE_GPUS=$(python3 -c "
import subprocess
result = subprocess.run(['nvidia-smi', '--query-gpu=index,memory.used,utilization.gpu', '--format=csv,noheader,nounits'], capture_output=True, text=True)
free_gpus = []
for line in result.stdout.strip().split('\n'):
    if line.strip():
        parts = line.split(',')
        if len(parts) >= 3:
            idx, mem_used, util = int(parts[0].strip()), int(parts[1].strip()), int(parts[2].strip())
            if mem_used < 1000 and util < 10:
                free_gpus.append(str(idx))
print(','.join(free_gpus))
")

if [ -z "$FREE_GPUS" ]; then
    echo "❌ 没有找到空闲的GPU，请检查GPU状态"
    exit 1
fi

echo "空闲GPU: $FREE_GPUS"
echo ""
echo "选择训练模式:"
echo "1) 自动选择最佳配置"
echo "2) 单GPU训练 (稳定)"
echo "3) 多GPU训练 (快速)"
echo "4) 自定义GPU选择"
echo "5) 仅显示状态，不训练"

read -p "请选择 (1-5): " choice

case $choice in
    1)
        echo "🚀 使用自动选择的最佳配置..."
        export CUDA_VISIBLE_DEVICES=$FREE_GPUS
        GPU_COUNT=$(echo $FREE_GPUS | tr ',' '\n' | wc -l)
        
        if [ $GPU_COUNT -eq 1 ]; then
            echo "启动单GPU训练..."
            llamafactory-cli train \
                --stage sft \
                --do_train \
                --model_name_or_path ~/qwen-finetune/models/Qwen/Qwen2.5-3B-Instruct \
                --dataset infant_nutrition \
                --dataset_dir ~/qwen-finetune/datasets/3-meal \
                --template qwen \
                --finetuning_type lora \
                --output_dir ./saves \
                --per_device_train_batch_size 2 \
                --gradient_accumulation_steps 6 \
                --lr_scheduler_type cosine \
                --logging_steps 10 \
                --save_steps 100 \
                --fp16 \
                --gradient_checkpointing
        else
            echo "启动${GPU_COUNT}GPU分布式训练..."
            GRAD_ACCUM=$((12 / GPU_COUNT))
            torchrun \
                --nnodes=1 \
                --nproc_per_node=$GPU_COUNT \
                --master_port=29500 \
                llamafactory-cli train \
                --stage sft \
                --do_train \
                --model_name_or_path ~/qwen-finetune/models/Qwen/Qwen2.5-3B-Instruct \
                --dataset infant_nutrition \
                --dataset_dir ~/qwen-finetune/datasets/3-meal \
                --template qwen \
                --finetuning_type lora \
                --output_dir ./saves \
                --per_device_train_batch_size 1 \
                --gradient_accumulation_steps $GRAD_ACCUM \
                --lr_scheduler_type cosine \
                --logging_steps 10 \
                --save_steps 100 \
                --fp16 \
                --gradient_checkpointing
        fi
        ;;
    2)
        FIRST_GPU=$(echo $FREE_GPUS | cut -d',' -f1)
        echo "🚀 使用单GPU训练 (GPU $FIRST_GPU)..."
        export CUDA_VISIBLE_DEVICES=$FIRST_GPU
        llamafactory-cli train \
            --stage sft \
            --do_train \
            --model_name_or_path ~/qwen-finetune/models/Qwen/Qwen2.5-3B-Instruct \
            --dataset infant_nutrition \
            --dataset_dir ~/qwen-finetune/datasets/3-meal \
            --template qwen \
            --finetuning_type lora \
            --output_dir ./saves \
            --per_device_train_batch_size 2 \
            --gradient_accumulation_steps 6 \
            --lr_scheduler_type cosine \
            --logging_steps 10 \
            --save_steps 100 \
            --fp16 \
            --gradient_checkpointing
        ;;
    3)
        export CUDA_VISIBLE_DEVICES=$FREE_GPUS
        GPU_COUNT=$(echo $FREE_GPUS | tr ',' '\n' | wc -l)
        echo "🚀 使用${GPU_COUNT}GPU分布式训练..."
        GRAD_ACCUM=$((12 / GPU_COUNT))
        torchrun \
            --nnodes=1 \
            --nproc_per_node=$GPU_COUNT \
            --master_port=29500 \
            llamafactory-cli train \
            --stage sft \
            --do_train \
            --model_name_or_path ~/qwen-finetune/models/Qwen/Qwen2.5-3B-Instruct \
            --dataset infant_nutrition \
            --dataset_dir ~/qwen-finetune/datasets/3-meal \
            --template qwen \
            --finetuning_type lora \
            --output_dir ./saves \
            --per_device_train_batch_size 1 \
            --gradient_accumulation_steps $GRAD_ACCUM \
            --lr_scheduler_type cosine \
            --logging_steps 10 \
            --save_steps 100 \
            --fp16 \
            --gradient_checkpointing
        ;;
    4)
        echo "可用的空闲GPU: $FREE_GPUS"
        read -p "请输入要使用的GPU编号 (用逗号分隔): " CUSTOM_GPUS
        export CUDA_VISIBLE_DEVICES=$CUSTOM_GPUS
        GPU_COUNT=$(echo $CUSTOM_GPUS | tr ',' '\n' | wc -l)
        
        if [ $GPU_COUNT -eq 1 ]; then
            echo "🚀 使用自定义单GPU训练..."
            llamafactory-cli train \
                --stage sft \
                --do_train \
                --model_name_or_path ~/qwen-finetune/models/Qwen/Qwen2.5-3B-Instruct \
                --dataset infant_nutrition \
                --dataset_dir ~/qwen-finetune/datasets/3-meal \
                --template qwen \
                --finetuning_type lora \
                --output_dir ./saves \
                --per_device_train_batch_size 2 \
                --gradient_accumulation_steps 6 \
                --lr_scheduler_type cosine \
                --logging_steps 10 \
                --save_steps 100 \
                --fp16 \
                --gradient_checkpointing
        else
            echo "🚀 使用自定义${GPU_COUNT}GPU分布式训练..."
            GRAD_ACCUM=$((12 / GPU_COUNT))
            torchrun \
                --nnodes=1 \
                --nproc_per_node=$GPU_COUNT \
                --master_port=29500 \
                llamafactory-cli train \
                --stage sft \
                --do_train \
                --model_name_or_path ~/qwen-finetune/models/Qwen/Qwen2.5-3B-Instruct \
                --dataset infant_nutrition \
                --dataset_dir ~/qwen-finetune/datasets/3-meal \
                --template qwen \
                --finetuning_type lora \
                --output_dir ./saves \
                --per_device_train_batch_size 1 \
                --gradient_accumulation_steps $GRAD_ACCUM \
                --lr_scheduler_type cosine \
                --logging_steps 10 \
                --save_steps 100 \
                --fp16 \
                --gradient_checkpointing
        fi
        ;;
    5)
        echo "✅ 仅显示状态，不启动训练"
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac
