#!/usr/bin/env python3
"""
快速找到空闲GPU的简化脚本
"""

import subprocess
import sys

def find_free_gpus():
    """快速找到空闲GPU"""
    try:
        # 获取GPU信息
        result = subprocess.run([
            'nvidia-smi', 
            '--query-gpu=index,name,memory.used,memory.total,utilization.gpu',
            '--format=csv,noheader,nounits'
        ], capture_output=True, text=True, check=True)
        
        free_gpus = []
        rtx_2080ti = []
        rtx_3090 = []
        
        print("🔍 扫描GPU状态...")
        print("-" * 60)
        
        for line in result.stdout.strip().split('\n'):
            if line.strip():
                parts = [p.strip() for p in line.split(',')]
                if len(parts) >= 5:
                    idx = int(parts[0])
                    name = parts[1]
                    mem_used = int(parts[2])
                    mem_total = int(parts[3])
                    util = int(parts[4])
                    
                    # 判断是否空闲 (显存使用<1GB 且 使用率<10%)
                    is_free = mem_used < 1000 and util < 10
                    status = "🟢 空闲" if is_free else "🔴 占用"
                    
                    print(f"GPU {idx}: {name:<20} {mem_used:>5}/{mem_total}MB {util:>3}% {status}")
                    
                    if is_free:
                        free_gpus.append(idx)
                        if "2080 Ti" in name:
                            rtx_2080ti.append(idx)
                        elif "3090" in name:
                            rtx_3090.append(idx)
        
        print("-" * 60)
        
        if not free_gpus:
            print("❌ 没有找到空闲的GPU")
            return
        
        print(f"✅ 找到 {len(free_gpus)} 张空闲GPU: {free_gpus}")
        
        # 推荐配置
        recommendations = []
        
        if len(rtx_2080ti) >= 3:
            recommendations.append({
                'name': f'{len(rtx_2080ti)}张RTX 2080 Ti',
                'gpus': rtx_2080ti[:4],  # 最多用4张
                'reason': '显存充足，适合LoRA微调'
            })
        
        if len(rtx_3090) >= 1:
            recommendations.append({
                'name': f'{min(len(rtx_3090), 2)}张RTX 3090',
                'gpus': rtx_3090[:2],  # 最多用2张
                'reason': '大显存，训练速度快'
            })
        
        if len(free_gpus) >= 2:
            # 混合使用
            mixed = (rtx_3090[:1] + rtx_2080ti[:2])[:3]
            if len(mixed) >= 2:
                recommendations.append({
                    'name': '混合GPU',
                    'gpus': mixed,
                    'reason': '充分利用可用资源'
                })
        
        # 单GPU选项
        if rtx_3090:
            recommendations.append({
                'name': '单张RTX 3090',
                'gpus': [rtx_3090[0]],
                'reason': '简单稳定，适合测试'
            })
        elif rtx_2080ti:
            recommendations.append({
                'name': '单张RTX 2080 Ti',
                'gpus': [rtx_2080ti[0]],
                'reason': '简单稳定，适合测试'
            })
        
        # 显示推荐
        print("\n🚀 推荐配置:")
        for i, rec in enumerate(recommendations, 1):
            gpus_str = ','.join(map(str, rec['gpus']))
            print(f"\n{i}. {rec['name']} - {rec['reason']}")
            print(f"   export CUDA_VISIBLE_DEVICES={gpus_str}")
            
            if len(rec['gpus']) == 1:
                print("   # 单GPU训练命令")
                print("   llamafactory-cli train --stage sft --do_train \\")
                print("       --per_device_train_batch_size 2 --gradient_accumulation_steps 6 \\")
                print("       --fp16 --gradient_checkpointing \\")
                print("       # ... 其他参数")
            else:
                print(f"   # {len(rec['gpus'])}GPU分布式训练命令")
                print(f"   torchrun --nproc_per_node={len(rec['gpus'])} llamafactory-cli train \\")
                print("       --stage sft --do_train \\")
                print("       --per_device_train_batch_size 1 \\")
                print(f"       --gradient_accumulation_steps {12//len(rec['gpus'])} \\")
                print("       --fp16 --gradient_checkpointing \\")
                print("       # ... 其他参数")
        
        # 生成快速设置脚本
        if recommendations:
            best_rec = recommendations[0]
            gpus_str = ','.join(map(str, best_rec['gpus']))
            
            print(f"\n💡 快速开始 (推荐使用 {best_rec['name']}):")
            print(f"export CUDA_VISIBLE_DEVICES={gpus_str}")
            
            # 验证CUDA映射
            print(f"\n🔧 验证GPU映射:")
            print(f"export CUDA_VISIBLE_DEVICES={gpus_str}")
            print("python -c \"import torch; [print(f'CUDA {i}: {torch.cuda.get_device_name(i)}') for i in range(torch.cuda.device_count())]\"")
            
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == "__main__":
    find_free_gpus()
