#!/usr/bin/env python3
"""
自动找到空闲GPU并生成CUDA_VISIBLE_DEVICES配置的脚本
"""

import subprocess
import os
import torch
import argparse
from typing import List, Tuple, Dict

def get_nvidia_smi_info() -> List[Dict]:
    """获取nvidia-smi的GPU信息"""
    try:
        result = subprocess.run([
            'nvidia-smi', 
            '--query-gpu=index,name,memory.used,memory.total,utilization.gpu,temperature.gpu',
            '--format=csv,noheader,nounits'
        ], capture_output=True, text=True, check=True)
        
        gpus = []
        for line in result.stdout.strip().split('\n'):
            if line.strip():
                parts = [p.strip() for p in line.split(',')]
                if len(parts) >= 6:
                    gpus.append({
                        'nvidia_index': int(parts[0]),
                        'name': parts[1],
                        'memory_used': int(parts[2]),
                        'memory_total': int(parts[3]),
                        'utilization': int(parts[4]),
                        'temperature': int(parts[5])
                    })
        return gpus
    except Exception as e:
        print(f"错误：无法获取nvidia-smi信息: {e}")
        return []

def is_gpu_free(gpu_info: Dict, memory_threshold: int = 1000, util_threshold: int = 10) -> bool:
    """判断GPU是否空闲"""
    return (gpu_info['memory_used'] < memory_threshold and 
            gpu_info['utilization'] < util_threshold)

def get_cuda_gpu_mapping(nvidia_indices: List[int]) -> Dict[int, str]:
    """获取nvidia-smi索引到CUDA设备的映射关系"""
    if not nvidia_indices:
        return {}
    
    # 设置CUDA_VISIBLE_DEVICES并检查对应的CUDA设备
    original_cuda_devices = os.environ.get('CUDA_VISIBLE_DEVICES', '')
    os.environ['CUDA_VISIBLE_DEVICES'] = ','.join(map(str, nvidia_indices))
    
    try:
        # 重新初始化CUDA
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        mapping = {}
        for cuda_idx in range(torch.cuda.device_count()):
            gpu_name = torch.cuda.get_device_name(cuda_idx)
            mapping[cuda_idx] = gpu_name
        
        return mapping
    except Exception as e:
        print(f"警告：无法获取CUDA映射信息: {e}")
        return {}
    finally:
        # 恢复原始设置
        if original_cuda_devices:
            os.environ['CUDA_VISIBLE_DEVICES'] = original_cuda_devices
        else:
            os.environ.pop('CUDA_VISIBLE_DEVICES', None)

def find_free_gpus(gpu_type: str = None, min_memory: int = None, 
                   memory_threshold: int = 1000, util_threshold: int = 10) -> List[Dict]:
    """找到空闲的GPU"""
    all_gpus = get_nvidia_smi_info()
    if not all_gpus:
        return []
    
    free_gpus = []
    for gpu in all_gpus:
        # 检查是否空闲
        if not is_gpu_free(gpu, memory_threshold, util_threshold):
            continue
        
        # 检查GPU类型过滤
        if gpu_type and gpu_type.lower() not in gpu['name'].lower():
            continue
        
        # 检查最小显存要求
        if min_memory and gpu['memory_total'] < min_memory:
            continue
        
        free_gpus.append(gpu)
    
    return free_gpus

def print_gpu_status(gpus: List[Dict]):
    """打印GPU状态"""
    print("\n" + "="*80)
    print("GPU状态概览")
    print("="*80)
    print(f"{'索引':<4} {'型号':<20} {'显存使用':<15} {'使用率':<8} {'温度':<6} {'状态':<6}")
    print("-"*80)
    
    for gpu in gpus:
        memory_usage = f"{gpu['memory_used']}/{gpu['memory_total']}MB"
        memory_percent = f"({gpu['memory_used']/gpu['memory_total']*100:.1f}%)"
        utilization = f"{gpu['utilization']}%"
        temperature = f"{gpu['temperature']}°C"
        status = "空闲" if is_gpu_free(gpu) else "占用"
        
        print(f"{gpu['nvidia_index']:<4} {gpu['name']:<20} {memory_usage:<10} {memory_percent:<5} "
              f"{utilization:<8} {temperature:<6} {status:<6}")

def generate_training_commands(free_gpus: List[Dict], max_gpus: int = None):
    """生成训练命令"""
    if not free_gpus:
        print("\n❌ 没有找到空闲的GPU")
        return
    
    # 按显存大小排序，优先使用显存大的GPU
    free_gpus.sort(key=lambda x: x['memory_total'], reverse=True)
    
    if max_gpus:
        free_gpus = free_gpus[:max_gpus]
    
    nvidia_indices = [gpu['nvidia_index'] for gpu in free_gpus]
    cuda_visible_devices = ','.join(map(str, nvidia_indices))
    
    print(f"\n✅ 找到 {len(free_gpus)} 张空闲GPU:")
    for i, gpu in enumerate(free_gpus):
        print(f"  {i+1}. nvidia-smi GPU {gpu['nvidia_index']}: {gpu['name']} "
              f"({gpu['memory_total']}MB显存)")
    
    print(f"\n🚀 推荐的训练命令:")
    print(f"export CUDA_VISIBLE_DEVICES={cuda_visible_devices}")
    
    if len(free_gpus) == 1:
        print("\n# 单GPU训练")
        print("llamafactory-cli train \\")
        print("    --stage sft \\")
        print("    --do_train \\")
        print("    --model_name_or_path ~/qwen-finetune/models/Qwen/Qwen2.5-3B-Instruct \\")
        print("    --dataset infant_nutrition \\")
        print("    --dataset_dir ~/qwen-finetune/datasets/3-meal \\")
        print("    --template qwen \\")
        print("    --finetuning_type lora \\")
        print("    --output_dir ./saves \\")
        print("    --per_device_train_batch_size 2 \\")
        print("    --gradient_accumulation_steps 6 \\")
        print("    --lr_scheduler_type cosine \\")
        print("    --logging_steps 10 \\")
        print("    --save_steps 100 \\")
        print("    --fp16 \\")
        print("    --gradient_checkpointing")
    else:
        print(f"\n# 多GPU训练 ({len(free_gpus)}张GPU)")
        print("torchrun \\")
        print("    --nnodes=1 \\")
        print(f"    --nproc_per_node={len(free_gpus)} \\")
        print("    --master_port=29500 \\")
        print("    llamafactory-cli train \\")
        print("    --stage sft \\")
        print("    --do_train \\")
        print("    --model_name_or_path ~/qwen-finetune/models/Qwen/Qwen2.5-3B-Instruct \\")
        print("    --dataset infant_nutrition \\")
        print("    --dataset_dir ~/qwen-finetune/datasets/3-meal \\")
        print("    --template qwen \\")
        print("    --finetuning_type lora \\")
        print("    --output_dir ./saves \\")
        print("    --per_device_train_batch_size 1 \\")
        print(f"    --gradient_accumulation_steps {12//len(free_gpus)} \\")
        print("    --lr_scheduler_type cosine \\")
        print("    --logging_steps 10 \\")
        print("    --save_steps 100 \\")
        print("    --fp16 \\")
        print("    --gradient_checkpointing")

def main():
    parser = argparse.ArgumentParser(description='找到空闲的GPU并生成训练命令')
    parser.add_argument('--gpu-type', type=str, help='GPU类型过滤 (如: 2080, 3090)')
    parser.add_argument('--min-memory', type=int, help='最小显存要求 (MB)')
    parser.add_argument('--memory-threshold', type=int, default=1000, 
                       help='显存使用阈值 (MB), 低于此值认为空闲 (默认: 1000)')
    parser.add_argument('--util-threshold', type=int, default=10,
                       help='使用率阈值 (%), 低于此值认为空闲 (默认: 10)')
    parser.add_argument('--max-gpus', type=int, help='最大使用GPU数量')
    parser.add_argument('--show-all', action='store_true', help='显示所有GPU状态')
    
    args = parser.parse_args()
    
    # 获取所有GPU信息
    all_gpus = get_nvidia_smi_info()
    if not all_gpus:
        print("❌ 无法获取GPU信息")
        return
    
    # 显示所有GPU状态
    if args.show_all:
        print_gpu_status(all_gpus)
    
    # 找到空闲GPU
    free_gpus = find_free_gpus(
        gpu_type=args.gpu_type,
        min_memory=args.min_memory,
        memory_threshold=args.memory_threshold,
        util_threshold=args.util_threshold
    )
    
    # 生成训练命令
    generate_training_commands(free_gpus, args.max_gpus)

if __name__ == "__main__":
    main()
