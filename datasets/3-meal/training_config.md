# 婴幼儿营养喂养数据集 - Qwen微调完整指南

## 📊 数据集信息
- **总样本数**: 1488
- **训练集**: 1190 个样本 (80%)
- **验证集**: 148 个样本 (10%)
- **测试集**: 150 个样本 (10%)
- **平均输出长度**: 1438.8 字符
- **最大输出长度**: 2247 字符

### 年龄组分布
- 12-24个月: 1129 个样本 (75.9%)
- 9-12个月: 170 个样本 (11.4%)
- 6-9个月: 94 个样本 (6.3%)
- 6个月开始: 95 个样本 (6.4%)

## 🚀 快速开始

### 1. 环境准备
```bash
# 激活虚拟环境
source llm_training_env/bin/activate

# 安装依赖
pip install torch transformers datasets accelerate peft bitsandbytes
```

### 2. 开始训练
```bash
# 直接运行训练脚本
./run_training.sh

# 或者自定义参数
python finetune_qwen.py \
    --model_name_or_path Qwen/Qwen-7B-Chat \
    --train_file infant_nutrition_train.json \
    --validation_file infant_nutrition_validation.json \
    --output_dir ./qwen_nutrition_lora \
    --do_train --do_eval \
    --per_device_train_batch_size 2 \
    --learning_rate 2e-5 \
    --num_train_epochs 3 \
    --use_lora True
```

### 3. 测试模型
```bash
# 使用测试集测试
python test_model.py --num_samples 5

# 交互式测试
python test_model.py --interactive

# 对比原始模型
python test_model.py --no_lora --num_samples 3
```

## 🔧 训练参数配置

### 推荐配置（7B模型）
```yaml
# 基础参数
model_name: "Qwen/Qwen-7B-Chat"
max_source_length: 512
max_target_length: 1536
batch_size: 2  # 根据GPU内存调整
gradient_accumulation_steps: 8  # 有效batch_size = 16
learning_rate: 2e-5
num_epochs: 3
warmup_ratio: 0.1

# LoRA参数（推荐用于7B模型）
use_lora: true
lora_r: 16
lora_alpha: 32
lora_dropout: 0.1
target_modules: ["q_proj", "v_proj", "k_proj", "o_proj"]

# 优化设置
fp16: true
gradient_checkpointing: true
dataloader_num_workers: 4
```

### GPU内存需求
| 配置 | 显存需求 | 训练时间（估算） |
|------|----------|------------------|
| batch_size=1, LoRA | ~12GB | 2-3小时 |
| batch_size=2, LoRA | ~16GB | 1.5-2小时 |
| batch_size=4, LoRA | ~24GB | 1-1.5小时 |

## 📁 文件结构
```
datasets/3-meal/
├── infant_nutrition_alpaca_format_20250728_144636.json  # 原始数据
├── infant_nutrition_train.json                         # 训练集
├── infant_nutrition_validation.json                    # 验证集
├── infant_nutrition_test.json                          # 测试集
├── split_dataset.py                                    # 数据分割脚本
├── finetune_qwen.py                                    # 微调脚本
├── run_training.sh                                     # 训练启动脚本
├── test_model.py                                       # 模型测试脚本
├── training_config.md                                  # 配置说明
└── qwen_nutrition_lora/                                # 训练输出目录
    ├── adapter_config.json
    ├── adapter_model.bin
    ├── tokenizer_config.json
    └── runs/                                           # TensorBoard日志
```

## ⚠️ 注意事项

### 硬件要求
1. **GPU内存**: 建议16GB+显存（RTX 4090, A100等）
2. **系统内存**: 建议32GB+系统内存
3. **存储空间**: 模型和数据需要20GB+空间

### 训练建议
1. **从小batch_size开始**: 先用batch_size=1测试，再逐步增加
2. **监控验证集loss**: 避免过拟合，可以early stopping
3. **使用gradient_checkpointing**: 节省显存但会增加训练时间
4. **启用fp16**: 减少显存占用，加速训练

### 常见问题
1. **CUDA OOM**: 减小batch_size或启用gradient_checkpointing
2. **训练速度慢**: 增加batch_size或使用多GPU训练
3. **模型效果不好**: 增加训练轮数或调整学习率

## 📈 训练监控

### 查看训练日志
```bash
# 启动TensorBoard
tensorboard --logdir ./qwen_nutrition_lora/runs

# 查看训练进度
tail -f ./qwen_nutrition_lora/trainer_state.json
```

### 关键指标
- **train_loss**: 训练损失，应该持续下降
- **eval_loss**: 验证损失，用于判断是否过拟合
- **learning_rate**: 学习率变化曲线
- **train_runtime**: 训练时间统计

## 🎯 预期效果

微调后的模型应该在婴幼儿营养咨询方面表现更好：
- 更专业的术语使用
- 更结构化的回答格式
- 更符合年龄段的建议
- 更安全的营养指导
