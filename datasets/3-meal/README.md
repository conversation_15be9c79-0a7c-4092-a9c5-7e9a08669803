# 婴幼儿营养数据集 - 微调格式说明

## 📊 数据集概览

- **总样本数**: 1,488条高质量对话
- **文件大小**: 5.4MB
- **数据格式**: 指令-输入-输出三元组
- **最后更新**: 2025年7月28日
- **平均指令长度**: 74.4字符
- **平均输出长度**: 1,438.8字符

## 🎯 年龄段分布

| 年龄段 | 样本数 | 占比 |
|--------|--------|------|
| 12-24个月 | 1,129条 | 75.9% |
| 9-12个月 | 170条 | 11.4% |
| 6个月开始 | 95条 | 6.4% |
| 6-9个月 | 94条 | 6.3% |

## 🔧 咨询类型分布

| 咨询类型 | 样本数 | 占比 | 说明 |
|----------|--------|------|------|
| 营养分析 | 271条 | 18.2% | 分析餐食营养价值和适宜性 |
| 质地建议 | 261条 | 17.5% | 食物质地和制作方法指导 |
| 喂养指导 | 259条 | 17.4% | 日常喂养安排和方法 |
| 分量指导 | 242条 | 16.3% | 食物分量和摄入量建议 |
| 问题解决 | 229条 | 15.4% | 喂养问题和挑食解决 |
| 安全建议 | 226条 | 15.2% | 食品安全和窒息预防 |

## 📁 转换格式说明

### 1. Alpaca格式 (`alpaca_format`)
**适用于**: Stanford Alpaca, Vicuna, Chinese-Alpaca等模型微调

```json
{
  "instruction": "用户问题",
  "input": "年龄段: 12-24个月幼儿\n咨询类型: 喂养指导\n营养转换: 287.0kcal → 360.0kcal",
  "output": "专业营养指导回答",
  "metadata": {
    "source": "infant_nutrition_dataset",
    "age_group": "12-24m",
    "type": "feeding_guidance",
    "model": "qwen-plus-latest",
    "based_on_guidelines": true
  }
}
```

**特点**:
- `input`字段包含年龄段、咨询类型、营养转换信息
- 提供丰富的上下文信息
- 保留完整的元数据



**特点**:
- 保留最完整的元数据信息
- 空的input字段，所有信息在instruction中
- 便于自定义处理

## 🚀 微调使用建议

### 1. 数据预处理
```python
import json

# 加载数据
with open('infant_nutrition_alpaca_format_xxx.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

# 过滤特定年龄段
target_age_data = [item for item in data if item['metadata']['age_group'] == '12-24m']

# 按咨询类型分组
from collections import defaultdict
type_groups = defaultdict(list)
for item in data:
    type_groups[item['metadata']['type']].append(item)
```

### 2. 训练参数建议
- **学习率**: 1e-5 到 5e-5
- **批次大小**: 4-8 (根据GPU内存调整)
- **训练轮数**: 3-5轮
- **最大序列长度**: 2048 (考虑到输出较长)
- **梯度累积**: 4-8步

### 3. 验证集划分
建议按年龄段和咨询类型分层采样，保持80:20的训练验证比例。

### 4. 评估指标
- **BLEU分数**: 评估生成质量
- **Rouge分数**: 评估内容覆盖度
- **专业术语准确性**: 人工评估
- **安全性检查**: 确保符合婴幼儿安全标准

## ⚠️ 重要注意事项

1. **专业性要求**: 本数据集涉及婴幼儿健康，微调后的模型应仅用于辅助参考，不能替代专业医疗建议。

2. **安全性检查**: 微调过程中应特别关注食品安全相关的输出，确保不会生成有害建议。

3. **年龄段适配**: 不同年龄段的营养需求差异很大，建议根据目标应用场景选择相应的年龄段数据。

4. **持续更新**: 营养指南可能更新，建议定期检查数据的时效性。

## 📞 技术支持

如有微调过程中的技术问题，可以参考：
- 各模型官方微调文档
- 数据集元数据中的generation_method信息
- 原始数据集的质量标记

---

**数据集版本**: v1.0  
**生成时间**: 2025-01-28  
**数据质量**: 基于官方指南，经过专业营养转换系统处理
