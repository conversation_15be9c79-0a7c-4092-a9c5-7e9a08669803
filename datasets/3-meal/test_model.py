#!/usr/bin/env python3
"""
微调后模型推理测试脚本
"""

import json
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import PeftModel
import argparse
from typing import List, Dict, Any
import random


def load_model_and_tokenizer(base_model_path: str, lora_model_path: str = None):
    """加载模型和tokenizer"""
    print(f"📖 加载tokenizer: {base_model_path}")
    tokenizer = AutoTokenizer.from_pretrained(
        base_model_path,
        trust_remote_code=True
    )
    
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    print(f"📖 加载基础模型: {base_model_path}")
    model = AutoModelForCausalLM.from_pretrained(
        base_model_path,
        torch_dtype=torch.float16,
        device_map="auto",
        trust_remote_code=True
    )
    
    if lora_model_path:
        print(f"📖 加载LoRA权重: {lora_model_path}")
        model = PeftModel.from_pretrained(model, lora_model_path)
        model = model.merge_and_unload()  # 合并LoRA权重
    
    model.eval()
    return model, tokenizer


def format_prompt(instruction: str, input_text: str = "") -> str:
    """格式化提示词"""
    if input_text.strip():
        prompt = f"### 指令:\n{instruction}\n\n### 输入:\n{input_text}\n\n### 输出:\n"
    else:
        prompt = f"### 指令:\n{instruction}\n\n### 输出:\n"
    return prompt


def generate_response(model, tokenizer, prompt: str, max_length: int = 2048) -> str:
    """生成回答"""
    inputs = tokenizer(prompt, return_tensors="pt")
    inputs = {k: v.to(model.device) for k, v in inputs.items()}
    
    with torch.no_grad():
        outputs = model.generate(
            **inputs,
            max_length=max_length,
            temperature=0.7,
            top_p=0.9,
            do_sample=True,
            pad_token_id=tokenizer.pad_token_id,
            eos_token_id=tokenizer.eos_token_id
        )
    
    # 解码输出，去除输入部分
    input_length = inputs['input_ids'].shape[1]
    generated_tokens = outputs[0][input_length:]
    response = tokenizer.decode(generated_tokens, skip_special_tokens=True)
    
    return response.strip()


def test_with_samples(model, tokenizer, test_file: str, num_samples: int = 5):
    """使用测试集样本进行测试"""
    print(f"\n🧪 使用测试集进行推理测试")
    print("=" * 60)
    
    # 加载测试数据
    with open(test_file, 'r', encoding='utf-8') as f:
        test_data = json.load(f)
    
    # 随机选择样本
    samples = random.sample(test_data, min(num_samples, len(test_data)))
    
    for i, sample in enumerate(samples, 1):
        print(f"\n📝 测试样本 {i}/{len(samples)}")
        print("-" * 40)
        
        # 构建提示词
        prompt = format_prompt(sample['instruction'], sample['input'])
        
        print(f"指令: {sample['instruction']}")
        print(f"输入: {sample['input']}")
        print(f"\n🤖 模型回答:")
        
        # 生成回答
        response = generate_response(model, tokenizer, prompt)
        print(response)
        
        print(f"\n✅ 标准答案:")
        print(sample['output'][:200] + "..." if len(sample['output']) > 200 else sample['output'])
        
        print("\n" + "="*60)


def interactive_test(model, tokenizer):
    """交互式测试"""
    print(f"\n💬 交互式测试模式")
    print("输入 'quit' 退出")
    print("=" * 60)
    
    while True:
        try:
            instruction = input("\n请输入指令: ").strip()
            if instruction.lower() == 'quit':
                break
            
            input_text = input("请输入具体问题（可选，直接回车跳过）: ").strip()
            
            prompt = format_prompt(instruction, input_text)
            print(f"\n🤖 模型回答:")
            
            response = generate_response(model, tokenizer, prompt)
            print(response)
            
        except KeyboardInterrupt:
            print("\n\n👋 退出交互模式")
            break
        except Exception as e:
            print(f"❌ 生成失败: {e}")


def main():
    parser = argparse.ArgumentParser(description="测试微调后的Qwen模型")
    parser.add_argument("--base_model", "-b", 
                       default="Qwen/Qwen-7B-Chat",
                       help="基础模型路径")
    parser.add_argument("--lora_model", "-l",
                       default="./qwen_nutrition_lora",
                       help="LoRA模型路径")
    parser.add_argument("--test_file", "-t",
                       default="infant_nutrition_test.json",
                       help="测试数据文件")
    parser.add_argument("--num_samples", "-n", type=int, default=3,
                       help="测试样本数量")
    parser.add_argument("--interactive", "-i", action="store_true",
                       help="启用交互式测试")
    parser.add_argument("--no_lora", action="store_true",
                       help="不加载LoRA权重，测试原始模型")
    
    args = parser.parse_args()
    
    print("🚀 开始模型推理测试")
    print("=" * 60)
    
    # 加载模型
    lora_path = None if args.no_lora else args.lora_model
    model, tokenizer = load_model_and_tokenizer(args.base_model, lora_path)
    
    print(f"✅ 模型加载完成")
    print(f"设备: {next(model.parameters()).device}")
    print(f"参数量: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")
    
    # 测试集推理
    if not args.interactive:
        test_with_samples(model, tokenizer, args.test_file, args.num_samples)
    
    # 交互式测试
    if args.interactive:
        interactive_test(model, tokenizer)
    
    print("\n🎉 测试完成！")


if __name__ == "__main__":
    main()
