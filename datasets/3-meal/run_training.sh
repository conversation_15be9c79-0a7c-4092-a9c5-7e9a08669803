#!/bin/bash

# 婴幼儿营养数据集 Qwen模型微调训练脚本
# 使用LoRA进行参数高效微调

set -e

echo "🚀 开始Qwen模型微调训练..."
echo "数据集: 婴幼儿营养喂养"
echo "模型: Qwen-7B-Chat"
echo "方法: LoRA微调"
echo "=" * 50

# 检查GPU
if command -v nvidia-smi &> /dev/null; then
    echo "📊 GPU信息:"
    nvidia-smi --query-gpu=name,memory.total,memory.free --format=csv,noheader,nounits
    echo ""
else
    echo "⚠️  未检测到NVIDIA GPU，将使用CPU训练（速度较慢）"
fi

# 检查数据文件
echo "📁 检查数据文件..."
for file in "infant_nutrition_train.json" "infant_nutrition_validation.json"; do
    if [ -f "$file" ]; then
        echo "  ✅ $file 存在"
    else
        echo "  ❌ $file 不存在，请先运行 python split_dataset.py"
        exit 1
    fi
done

# 创建输出目录
OUTPUT_DIR="./qwen_nutrition_lora"
mkdir -p $OUTPUT_DIR
echo "📂 输出目录: $OUTPUT_DIR"

# 设置环境变量
export CUDA_VISIBLE_DEVICES=0  # 使用第一块GPU，根据实际情况调整
export TOKENIZERS_PARALLELISM=false

# 训练参数
MODEL_NAME="Qwen/Qwen-7B-Chat"  # 或者你的本地模型路径
BATCH_SIZE=2                    # 根据GPU内存调整
GRADIENT_ACCUMULATION_STEPS=8   # 有效batch_size = BATCH_SIZE * GRADIENT_ACCUMULATION_STEPS
LEARNING_RATE=2e-5
NUM_EPOCHS=3
MAX_SOURCE_LENGTH=512
MAX_TARGET_LENGTH=1536
WARMUP_RATIO=0.1
SAVE_STEPS=100
EVAL_STEPS=100
LOGGING_STEPS=10

echo "🔧 训练参数:"
echo "  模型: $MODEL_NAME"
echo "  批次大小: $BATCH_SIZE"
echo "  梯度累积步数: $GRADIENT_ACCUMULATION_STEPS"
echo "  有效批次大小: $((BATCH_SIZE * GRADIENT_ACCUMULATION_STEPS))"
echo "  学习率: $LEARNING_RATE"
echo "  训练轮数: $NUM_EPOCHS"
echo "  最大输入长度: $MAX_SOURCE_LENGTH"
echo "  最大输出长度: $MAX_TARGET_LENGTH"
echo ""

# 开始训练
python finetune_qwen.py \
    --model_name_or_path $MODEL_NAME \
    --train_file infant_nutrition_train.json \
    --validation_file infant_nutrition_validation.json \
    --output_dir $OUTPUT_DIR \
    --overwrite_output_dir \
    --do_train \
    --do_eval \
    --per_device_train_batch_size $BATCH_SIZE \
    --per_device_eval_batch_size $BATCH_SIZE \
    --gradient_accumulation_steps $GRADIENT_ACCUMULATION_STEPS \
    --learning_rate $LEARNING_RATE \
    --num_train_epochs $NUM_EPOCHS \
    --max_source_length $MAX_SOURCE_LENGTH \
    --max_target_length $MAX_TARGET_LENGTH \
    --warmup_ratio $WARMUP_RATIO \
    --logging_steps $LOGGING_STEPS \
    --save_steps $SAVE_STEPS \
    --eval_steps $EVAL_STEPS \
    --evaluation_strategy steps \
    --save_strategy steps \
    --load_best_model_at_end \
    --metric_for_best_model eval_loss \
    --greater_is_better False \
    --save_total_limit 3 \
    --dataloader_num_workers 4 \
    --remove_unused_columns False \
    --fp16 \
    --gradient_checkpointing \
    --report_to tensorboard \
    --use_lora True \
    --lora_r 16 \
    --lora_alpha 32 \
    --lora_dropout 0.1 \
    --lora_target_modules "q_proj,v_proj,k_proj,o_proj"

echo ""
echo "🎉 训练完成！"
echo "📁 模型保存在: $OUTPUT_DIR"
echo "📊 查看训练日志: tensorboard --logdir $OUTPUT_DIR/runs"
echo ""
echo "📝 下一步:"
echo "1. 查看训练日志和损失曲线"
echo "2. 使用测试集评估模型性能"
echo "3. 部署模型进行推理测试"
