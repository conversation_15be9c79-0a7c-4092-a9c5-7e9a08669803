#!/usr/bin/env python3
"""
数据集分割脚本
将营养喂养数据集分割为训练集和评估集，用于微调Qwen模型
"""

import json
import random
from pathlib import Path
from typing import List, Dict, Any
import argparse


def load_dataset(file_path: str) -> List[Dict[str, Any]]:
    """加载数据集"""
    print(f"📖 加载数据集: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print(f"✅ 成功加载 {len(data)} 个样本")
    return data


def analyze_dataset(data: List[Dict[str, Any]]) -> Dict[str, Any]:
    """分析数据集统计信息"""
    print("\n📊 数据集分析:")
    print("=" * 50)
    
    # 基本统计
    total_samples = len(data)
    print(f"总样本数: {total_samples}")
    
    # 检查数据格式
    sample = data[0]
    required_fields = ['instruction', 'input', 'output']
    missing_fields = [field for field in required_fields if field not in sample]
    
    if missing_fields:
        print(f"❌ 缺少必要字段: {missing_fields}")
        return {}
    
    print(f"✅ 数据格式正确，包含字段: {list(sample.keys())}")
    
    # 文本长度统计
    instruction_lengths = [len(item['instruction']) for item in data]
    input_lengths = [len(item['input']) for item in data]
    output_lengths = [len(item['output']) for item in data]
    
    stats = {
        'total_samples': total_samples,
        'avg_instruction_length': sum(instruction_lengths) / len(instruction_lengths),
        'avg_input_length': sum(input_lengths) / len(input_lengths),
        'avg_output_length': sum(output_lengths) / len(output_lengths),
        'max_output_length': max(output_lengths),
        'min_output_length': min(output_lengths)
    }
    
    print(f"平均指令长度: {stats['avg_instruction_length']:.1f} 字符")
    print(f"平均输入长度: {stats['avg_input_length']:.1f} 字符")
    print(f"平均输出长度: {stats['avg_output_length']:.1f} 字符")
    print(f"输出长度范围: {stats['min_output_length']} - {stats['max_output_length']} 字符")
    
    # 年龄组分析（如果有metadata）
    if 'metadata' in data[0] and 'age_group' in data[0]['metadata']:
        age_groups = {}
        for item in data:
            age_group = item['metadata'].get('age_group', 'unknown')
            age_groups[age_group] = age_groups.get(age_group, 0) + 1
        
        print(f"\n年龄组分布:")
        for age_group, count in sorted(age_groups.items()):
            print(f"  {age_group}: {count} 个样本 ({count/total_samples*100:.1f}%)")
        
        stats['age_groups'] = age_groups
    
    return stats


def split_dataset(data: List[Dict[str, Any]], 
                 train_ratio: float = 0.8, 
                 val_ratio: float = 0.1, 
                 test_ratio: float = 0.1,
                 random_seed: int = 42) -> Dict[str, List[Dict[str, Any]]]:
    """
    分割数据集为训练集、验证集和测试集
    
    Args:
        data: 原始数据
        train_ratio: 训练集比例
        val_ratio: 验证集比例  
        test_ratio: 测试集比例
        random_seed: 随机种子
    """
    print(f"\n🔀 分割数据集 (训练:{train_ratio*100}%, 验证:{val_ratio*100}%, 测试:{test_ratio*100}%)")
    
    # 检查比例
    if abs(train_ratio + val_ratio + test_ratio - 1.0) > 1e-6:
        raise ValueError("训练集、验证集、测试集比例之和必须等于1.0")
    
    # 设置随机种子
    random.seed(random_seed)
    
    # 打乱数据
    data_shuffled = data.copy()
    random.shuffle(data_shuffled)
    
    total_samples = len(data_shuffled)
    train_size = int(total_samples * train_ratio)
    val_size = int(total_samples * val_ratio)
    
    # 分割数据
    train_data = data_shuffled[:train_size]
    val_data = data_shuffled[train_size:train_size + val_size]
    test_data = data_shuffled[train_size + val_size:]
    
    print(f"✅ 分割完成:")
    print(f"  训练集: {len(train_data)} 个样本")
    print(f"  验证集: {len(val_data)} 个样本")
    print(f"  测试集: {len(test_data)} 个样本")
    
    return {
        'train': train_data,
        'validation': val_data,
        'test': test_data
    }


def save_split_datasets(split_data: Dict[str, List[Dict[str, Any]]], 
                       output_dir: str = ".") -> Dict[str, str]:
    """保存分割后的数据集"""
    print(f"\n💾 保存分割后的数据集到: {output_dir}")
    
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    saved_files = {}
    
    for split_name, data in split_data.items():
        if not data:  # 跳过空数据集
            continue
            
        filename = f"infant_nutrition_{split_name}.json"
        filepath = output_path / filename
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        saved_files[split_name] = str(filepath)
        print(f"  ✅ {split_name}: {filename} ({len(data)} 个样本)")
    
    return saved_files


def create_training_config(stats: Dict[str, Any], saved_files: Dict[str, str]) -> str:
    """创建训练配置文件"""
    config_content = f"""# 婴幼儿营养喂养数据集 - 微调配置

## 数据集信息
- 总样本数: {stats['total_samples']}
- 平均输出长度: {stats['avg_output_length']:.1f} 字符
- 最大输出长度: {stats['max_output_length']} 字符

## 文件路径
- 训练集: {saved_files.get('train', 'N/A')}
- 验证集: {saved_files.get('validation', 'N/A')}
- 测试集: {saved_files.get('test', 'N/A')}

## 推荐训练参数
```yaml
# 基础参数
model_name: "Qwen/Qwen-7B-Chat"
max_length: {min(2048, int(stats['max_output_length'] * 1.5))}
batch_size: 4  # 根据GPU内存调整
learning_rate: 2e-5
num_epochs: 3
warmup_ratio: 0.1

# LoRA参数（推荐用于7B模型）
use_lora: true
lora_r: 16
lora_alpha: 32
lora_dropout: 0.1
target_modules: ["q_proj", "v_proj", "k_proj", "o_proj"]

# 数据处理
train_file: "{saved_files.get('train', '')}"
validation_file: "{saved_files.get('validation', '')}"
test_file: "{saved_files.get('test', '')}"
```

## 注意事项
1. 根据GPU内存调整batch_size（建议从2开始测试）
2. 监控验证集loss，避免过拟合
3. 可以使用gradient_checkpointing节省内存
4. 建议使用fp16或bf16精度训练
"""
    
    config_path = "training_config.md"
    with open(config_path, 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print(f"📋 训练配置已保存到: {config_path}")
    return config_path


def main():
    parser = argparse.ArgumentParser(description="分割婴幼儿营养数据集")
    parser.add_argument("--input", "-i", 
                       default="infant_nutrition_alpaca_format_20250728_144636.json",
                       help="输入数据集文件路径")
    parser.add_argument("--output", "-o", default=".", 
                       help="输出目录")
    parser.add_argument("--train-ratio", type=float, default=0.8,
                       help="训练集比例 (默认: 0.8)")
    parser.add_argument("--val-ratio", type=float, default=0.1,
                       help="验证集比例 (默认: 0.1)")
    parser.add_argument("--test-ratio", type=float, default=0.1,
                       help="测试集比例 (默认: 0.1)")
    parser.add_argument("--seed", type=int, default=42,
                       help="随机种子 (默认: 42)")
    
    args = parser.parse_args()
    
    print("🚀 开始处理婴幼儿营养数据集")
    print("=" * 60)
    
    try:
        # 1. 加载数据集
        data = load_dataset(args.input)
        
        # 2. 分析数据集
        stats = analyze_dataset(data)
        if not stats:
            return
        
        # 3. 分割数据集
        split_data = split_dataset(
            data, 
            train_ratio=args.train_ratio,
            val_ratio=args.val_ratio, 
            test_ratio=args.test_ratio,
            random_seed=args.seed
        )
        
        # 4. 保存分割后的数据集
        saved_files = save_split_datasets(split_data, args.output)
        
        # 5. 创建训练配置
        create_training_config(stats, saved_files)
        
        print(f"\n🎉 数据集处理完成！")
        print(f"📁 输出目录: {Path(args.output).absolute()}")
        print(f"🔧 下一步: 查看 training_config.md 开始微调训练")
        
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
