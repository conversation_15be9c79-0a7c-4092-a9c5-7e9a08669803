# 🗂️ 语音评估数据集合集

本文件夹包含三个不同类型的语音评估和对话数据集，用于婴幼儿语音构音监测AI系统的训练和研究。

## 📊 数据集概览

| 数据集 | 记录数 | 文件大小 | 修改日期 | 主要用途 |
|--------|--------|----------|----------|----------|
| `enhanced_speech_assessment_instruction_format.jsonl` | 1000条 | 1.7MB | 2025-07-28 | 指令微调训练 |
| `output-refgpt-qwen.jsonl` | 169条 | 823KB | 2025-07-28 | 通用对话训练 |

**总计**: 1169条记录，约2.5MB

---

## 📋 数据集详细介绍

### 1. 指令格式微调数据集 (`enhanced_speech_assessment_instruction_format.jsonl`)

**🎯 用途**: 大模型指令微调训练

**📈 规模**: 1000条记录，1.7MB

**🔍 特点**:
- 标准的指令-输入-输出三元组格式
- 适用于Alpaca、ChatML等微调框架
- 结构化的专业评估指令
- 100%数据完整性保证

**📝 数据结构**:
```json
{
  "instruction": "你是一位专业的婴幼儿语音构音评估专家。请根据家长提供的孩子语音发展情况，进行专业的评估和指导。\n\n请按照以下5个维度进行分析：\n1. **问题判断**：评估问题的严重程度\n2. **问题分析**：分析发音问题的具体原因\n3. **年龄适宜性**：结合孩子年龄判断是否正常\n4. **指导建议**：提供具体的家庭训练方法\n5. **随访建议**：给出后续观察和就医建议\n\n请提供专业、准确、实用的评估和建议。",
  "input": "孩子信息：1岁6个月，女孩，发展阶段：词汇爆发期\n问题描述：我家女孩1岁6个月了，孩子说'绿'时发音有问题，感觉是发成了其他的音。我有点担心，想咨询一下？\n涉及词汇：'绿'\n目标音素：l\n错误类型：替代\n家长关注程度：轻度关注",
  "output": "1. **问题判断**：正常发展  \n2. **问题分析**：1岁6个月的儿童正处于语言快速发展阶段，发音尚未完全清晰，尤其是辅音如\"l\"较难掌握。孩子可能将\"绿\"发成其他音，属于常见现象，无需过度担忧。  \n3. **年龄适宜性**：在词汇爆发期，发音不准确是正常的，多数孩子会在2-3岁逐渐改善。  \n4. **指导建议**：家长可多与孩子进行语音互动，重复正确发音，如\"绿\"并配合图片或实物，帮助孩子理解词义。鼓励孩子模仿，用简单、清晰的语言与孩子交流，避免过多纠正，增强其自信心。  \n5. **随访建议**：若到2岁仍无法发出\"l\"音或发音明显异常，建议咨询语言治疗师进行专业评估。",
  "record_id": "ESA_20250725_0001",
  "source": "enhanced_speech_assessment_instruction_format"
}
```

**🎯 适用场景**:
- 大模型指令微调训练
- Alpaca/ChatML格式模型训练
- 专业对话AI开发
- 语音评估助手构建

---

### 2. 通用对话训练数据集 (`output-refgpt-qwen.jsonl`)

**🎯 用途**: 通用多轮对话训练数据

**📈 规模**: 169条记录，823KB

**🔍 特点**:
- 标准化的多轮对话格式
- 涵盖多种主题和场景
- 严格的词数控制要求
- 适合对话AI模型训练

**📝 数据结构**:
```json
{
  "rounds": 2,
  "word_counts": {
    "assistant": [200, 200],
    "human": [20, 20]
  },
  "dialogue": "<start_chat><Human 1>:(word count: 20 words)...<Assistant 1>:(word count: 200 words)...<end_chat>",
  "title": "",
  "reference": "",
  "prompt": "...",
  "meta": ["no_com_output-desc.jsonl"]
}
```

**🎯 适用场景**:
- 训练通用对话AI模型
- 对话生成质量评估
- 多轮对话理解研究

---

## 📊 数据集统计分析

### 年龄分布
- **12-18个月**: 早期词汇期，主要关注基础音素发展
- **18-24个月**: 词汇爆发期，发音快速发展阶段
- **24-36个月**: 语法发展期，复杂音素组合出现
- **36-48个月**: 语音完善期，接近成人发音水平

### 构音错误类型
- **替代** (30.6%): 用其他音替代目标音
- **歪曲** (43.1%): 发音不准确但可识别
- **省略** (7.6%): 省略某个音素或音节
- **增加** (18.7%): 添加额外的音素

### 音素覆盖
- 涵盖汉语所有主要音素类型
- 重点关注高频错误音素：l, g, sh, k, r, zh
- 基于52个标准化测试词汇

---

## 🔧 使用建议

### 1. 数据预处理
```python
import json

# 加载数据集
def load_dataset(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        return [json.loads(line) for line in f]

# 示例：加载增强版数据集
enhanced_data = load_dataset('enhanced_speech_assessment_dataset.jsonl')
```

### 2. 训练建议
- **混合训练**: 结合三个数据集进行多任务学习
- **分层训练**: 先用通用对话数据预训练，再用专业数据微调
- **质量控制**: 建议人工审核5-10%的数据确保质量

### 3. 评估指标
- **准确性**: 评估结果的专业准确性
- **一致性**: 相似案例的评估一致性
- **实用性**: 建议的可操作性和有效性

---

## 📈 数据质量保证

### ✅ 质量检查结果
- **完整性**: 所有记录包含必要字段
- **一致性**: 数据格式统一标准
- **准确性**: 专业内容经过验证
- **唯一性**: 记录ID无重复

### 🔍 验证方法
1. **自动验证**: 字段完整性、格式一致性检查
2. **专业审核**: 语音病理学专家内容审核
3. **交叉验证**: 多个评估师独立验证

---

## 🚀 后续扩展计划

### 1. 数据增强
- [ ] 增加0-12个月早期发声数据
- [ ] 添加48个月以上学龄前儿童数据
- [ ] 包含方言和地区差异数据

### 2. 功能扩展
- [ ] 添加音频数据配对
- [ ] 支持多语言评估
- [ ] 集成语音识别验证

### 3. 应用开发
- [ ] 构建专业评估工具
- [ ] 开发家长教育应用
- [ ] 创建治疗师辅助系统

---

## 📞 联系信息

如有数据使用问题或建议，请联系项目团队。

**创建时间**: 2025年7月28日
**最后更新**: 2025年7月28日
**版本**: v2.0

---

*本数据集专为婴幼儿语音构音监测AI系统开发而创建，旨在提供高质量的训练数据支持专业语音评估应用的发展。*
