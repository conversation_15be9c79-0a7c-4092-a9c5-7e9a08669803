#!/usr/bin/env python3
"""
结果分析脚本
用于分析和可视化评估结果
"""

import json
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import argparse
from typing import Dict, List, Any

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


class ResultAnalyzer:
    """结果分析器"""
    
    def __init__(self, results_file: str):
        """初始化分析器"""
        self.results_file = Path(results_file)
        
        with open(self.results_file, 'r', encoding='utf-8') as f:
            self.results = json.load(f)
        
        self.model_name = self.results.get('model_name', 'Unknown')
        self.dataset_summaries = self.results.get('dataset_summaries', [])
        
    def print_summary(self):
        """打印评估摘要"""
        print(f"📊 评估结果摘要")
        print(f"=" * 50)
        print(f"模型名称: {self.model_name}")
        print(f"评估时间: {self.results.get('evaluation_time', 'Unknown')}")
        print(f"数据集数量: {self.results.get('dataset_count', 0)}")
        
        if 'overall_metrics' in self.results and self.results['overall_metrics']:
            metrics = self.results['overall_metrics']
            print(f"\n🎯 总体指标:")
            print(f"  平均综合分数: {metrics.get('avg_overall_score', 0):.3f}")
            print(f"  平均BLEU分数: {metrics.get('avg_bleu_score', 0):.3f}")
            print(f"  平均ROUGE-L分数: {metrics.get('avg_rouge_l', 0):.3f}")
            print(f"  平均安全性分数: {metrics.get('avg_safety_score', 0):.3f}")
            print(f"  平均专业准确性: {metrics.get('avg_professional_accuracy', 0):.3f}")
            print(f"  平均成功率: {metrics.get('avg_success_rate', 0):.1%}")
    
    def print_dataset_details(self):
        """打印各数据集详细结果"""
        if not self.dataset_summaries:
            print("❌ 没有找到数据集摘要信息")
            return
        
        print(f"\n📋 各数据集详细结果:")
        print(f"=" * 80)
        
        # 创建DataFrame便于格式化显示
        df = pd.DataFrame(self.dataset_summaries)
        
        # 格式化数值列
        numeric_columns = ['overall_score', 'bleu_score', 'rouge_l', 'safety_score', 'professional_accuracy', 'success_rate']
        for col in numeric_columns:
            if col in df.columns:
                if col == 'success_rate':
                    df[col] = df[col].apply(lambda x: f"{x:.1%}")
                else:
                    df[col] = df[col].apply(lambda x: f"{x:.3f}")
        
        print(df.to_string(index=False))
    
    def create_visualizations(self, output_dir: str = "analysis_plots"):
        """创建可视化图表"""
        if not self.dataset_summaries:
            print("❌ 没有数据可以可视化")
            return
        
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        df = pd.DataFrame(self.dataset_summaries)
        
        # 1. 各数据集综合分数对比
        plt.figure(figsize=(12, 6))
        plt.subplot(1, 2, 1)
        bars = plt.bar(df['dataset_name'], df['overall_score'])
        plt.title(f'{self.model_name} - 各数据集综合分数')
        plt.xlabel('数据集')
        plt.ylabel('综合分数')
        plt.xticks(rotation=45)
        
        # 添加数值标签
        for bar in bars:
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height,
                    f'{height:.3f}', ha='center', va='bottom')
        
        # 2. 各指标雷达图
        plt.subplot(1, 2, 2)
        metrics = ['bleu_score', 'rouge_l', 'safety_score', 'professional_accuracy']
        avg_scores = [df[metric].mean() for metric in metrics]
        
        angles = [i * 360 / len(metrics) for i in range(len(metrics))]
        angles += angles[:1]  # 闭合
        avg_scores += avg_scores[:1]  # 闭合
        
        plt.polar(angles, avg_scores, 'o-', linewidth=2)
        plt.fill(angles, avg_scores, alpha=0.25)
        plt.thetagrids(angles[:-1], [m.replace('_', ' ').title() for m in metrics])
        plt.title(f'{self.model_name} - 平均指标雷达图')
        plt.ylim(0, 1)
        
        plt.tight_layout()
        plt.savefig(output_path / f'{self.model_name}_overview.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        # 3. 详细指标热力图
        plt.figure(figsize=(10, 8))
        
        # 准备热力图数据
        heatmap_data = df.set_index('dataset_name')[metrics].astype(float)
        
        sns.heatmap(heatmap_data, annot=True, cmap='YlOrRd', fmt='.3f',
                   cbar_kws={'label': '分数'})
        plt.title(f'{self.model_name} - 各数据集指标热力图')
        plt.xlabel('评估指标')
        plt.ylabel('数据集')
        
        plt.tight_layout()
        plt.savefig(output_path / f'{self.model_name}_heatmap.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        # 4. 成功率和样本数量分析
        plt.figure(figsize=(12, 5))
        
        plt.subplot(1, 2, 1)
        plt.bar(df['dataset_name'], df['success_rate'])
        plt.title(f'{self.model_name} - 各数据集成功率')
        plt.xlabel('数据集')
        plt.ylabel('成功率')
        plt.xticks(rotation=45)
        
        plt.subplot(1, 2, 2)
        plt.bar(df['dataset_name'], df['sample_count'])
        plt.title(f'{self.model_name} - 各数据集样本数量')
        plt.xlabel('数据集')
        plt.ylabel('样本数量')
        plt.xticks(rotation=45)
        
        plt.tight_layout()
        plt.savefig(output_path / f'{self.model_name}_stats.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"📊 可视化图表已保存到: {output_path}")
    
    def export_detailed_csv(self, output_file: str = None):
        """导出详细的CSV报告"""
        if not self.dataset_summaries:
            print("❌ 没有数据可以导出")
            return
        
        if output_file is None:
            output_file = f"{self.model_name}_detailed_results.csv"
        
        df = pd.DataFrame(self.dataset_summaries)
        df.to_csv(output_file, index=False, encoding='utf-8')
        print(f"📄 详细CSV报告已保存到: {output_file}")
    
    def find_best_worst_datasets(self):
        """找出表现最好和最差的数据集"""
        if not self.dataset_summaries:
            return
        
        df = pd.DataFrame(self.dataset_summaries)
        
        # 按综合分数排序
        best_dataset = df.loc[df['overall_score'].idxmax()]
        worst_dataset = df.loc[df['overall_score'].idxmin()]
        
        print(f"\n🏆 表现最好的数据集:")
        print(f"  数据集: {best_dataset['dataset_name']}")
        print(f"  综合分数: {best_dataset['overall_score']:.3f}")
        print(f"  成功率: {best_dataset['success_rate']:.1%}")
        
        print(f"\n⚠️ 表现最差的数据集:")
        print(f"  数据集: {worst_dataset['dataset_name']}")
        print(f"  综合分数: {worst_dataset['overall_score']:.3f}")
        print(f"  成功率: {worst_dataset['success_rate']:.1%}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="分析评估结果")
    parser.add_argument('results_file', help='评估结果JSON文件路径')
    parser.add_argument('--plot', action='store_true', help='生成可视化图表')
    parser.add_argument('--csv', action='store_true', help='导出CSV报告')
    parser.add_argument('--output-dir', default='analysis_plots', help='图表输出目录')
    
    args = parser.parse_args()
    
    try:
        analyzer = ResultAnalyzer(args.results_file)
        
        # 打印摘要
        analyzer.print_summary()
        
        # 打印详细结果
        analyzer.print_dataset_details()
        
        # 找出最好和最差的数据集
        analyzer.find_best_worst_datasets()
        
        # 生成可视化图表
        if args.plot:
            analyzer.create_visualizations(args.output_dir)
        
        # 导出CSV
        if args.csv:
            analyzer.export_detailed_csv()
            
    except Exception as e:
        print(f"❌ 分析过程中出现错误: {e}")
        raise


if __name__ == "__main__":
    main()
