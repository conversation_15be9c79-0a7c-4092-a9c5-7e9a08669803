#!/usr/bin/env python3
"""
简化版测试脚本
只测试数据加载和基本功能，不依赖深度学习库
"""

import sys
import os
import json
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

from data_loader import create_data_loader


def test_config_loading():
    """测试配置文件加载"""
    print("🔍 测试配置文件加载...")
    
    try:
        import yaml
        
        config_path = Path(__file__).parent.parent / "config.yaml"
        if not config_path.exists():
            print(f"✗ 配置文件不存在: {config_path}")
            return False
            
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
            
        print(f"✓ 配置文件加载成功")
        print(f"  数据集数量: {len(config.get('datasets', {}))}")
        print(f"  模型数量: {len(config.get('models', {}))}")
        
        return True
        
    except Exception as e:
        print(f"✗ 配置文件加载失败: {e}")
        return False


def test_data_loading():
    """测试数据加载"""
    print("\n🔍 测试数据加载...")
    
    try:
        loader = create_data_loader()
        
        # 测试加载单个数据集
        dataset_name = 'phoneme'  # 选择一个存在的数据集
        
        if dataset_name not in loader.datasets_config:
            print(f"✗ 数据集 {dataset_name} 不在配置中")
            return False
            
        samples = loader.load_dataset(dataset_name, sample_size=3)
        print(f"✓ 数据集 {dataset_name} 加载成功: {len(samples)} 个样本")
        
        if samples:
            sample = samples[0]
            print(f"  样本ID: {sample.sample_id}")
            print(f"  指令长度: {len(sample.instruction)} 字符")
            print(f"  输入长度: {len(sample.input)} 字符")
            print(f"  输出长度: {len(sample.output)} 字符")
            print(f"  数据集名称: {sample.dataset_name}")
        
        # 获取统计信息
        stats = loader.get_dataset_stats(dataset_name)
        print(f"✓ 数据集统计: 总样本数 {stats['total_samples']}")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据加载测试失败: {e}")
        return False


def test_all_datasets():
    """测试所有数据集加载"""
    print("\n🔍 测试所有数据集加载...")
    
    try:
        loader = create_data_loader()
        
        success_count = 0
        total_count = len(loader.datasets_config)
        
        for dataset_name in loader.datasets_config.keys():
            try:
                samples = loader.load_dataset(dataset_name, sample_size=1)
                print(f"  ✓ {dataset_name}: {len(samples)} 个样本")
                success_count += 1
            except Exception as e:
                print(f"  ✗ {dataset_name}: {e}")
        
        print(f"\n📊 数据集加载结果: {success_count}/{total_count} 成功")
        return success_count == total_count
        
    except Exception as e:
        print(f"✗ 批量数据集测试失败: {e}")
        return False


def test_data_formats():
    """测试数据格式"""
    print("\n🔍 测试数据格式...")
    
    try:
        loader = create_data_loader()
        
        # 测试JSONL格式
        jsonl_datasets = [name for name, config in loader.datasets_config.items() 
                         if config.get('format') == 'jsonl']
        
        # 测试JSON格式
        json_datasets = [name for name, config in loader.datasets_config.items() 
                        if config.get('format') == 'json']
        
        print(f"  JSONL格式数据集: {len(jsonl_datasets)} 个")
        print(f"  JSON格式数据集: {len(json_datasets)} 个")
        
        # 测试一个JSONL数据集
        if jsonl_datasets:
            samples = loader.load_dataset(jsonl_datasets[0], sample_size=1)
            print(f"  ✓ JSONL格式测试成功: {jsonl_datasets[0]}")
        
        # 测试一个JSON数据集
        if json_datasets:
            samples = loader.load_dataset(json_datasets[0], sample_size=1)
            print(f"  ✓ JSON格式测试成功: {json_datasets[0]}")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据格式测试失败: {e}")
        return False


def test_sample_structure():
    """测试样本结构"""
    print("\n🔍 测试样本结构...")
    
    try:
        loader = create_data_loader()
        
        # 选择第一个可用的数据集
        dataset_name = list(loader.datasets_config.keys())[0]
        samples = loader.load_dataset(dataset_name, sample_size=1)
        
        if not samples:
            print("✗ 没有加载到样本")
            return False
        
        sample = samples[0]
        
        # 检查必要字段
        required_fields = ['instruction', 'input', 'output', 'dataset_name']
        missing_fields = []
        
        for field in required_fields:
            if not hasattr(sample, field):
                missing_fields.append(field)
        
        if missing_fields:
            print(f"✗ 样本缺少字段: {missing_fields}")
            return False
        
        print(f"✓ 样本结构正确")
        print(f"  必要字段: {required_fields}")
        print(f"  样本ID: {getattr(sample, 'sample_id', 'N/A')}")
        print(f"  元数据: {getattr(sample, 'metadata', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"✗ 样本结构测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始简化版测试...")
    print("=" * 50)
    
    tests = [
        ("配置文件加载", test_config_loading),
        ("数据加载", test_data_loading),
        ("所有数据集", test_all_datasets),
        ("数据格式", test_data_formats),
        ("样本结构", test_sample_structure),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        if test_func():
            passed += 1
    
    print(f"\n📈 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 基础功能测试通过！")
        print("\n📝 下一步:")
        print("1. 安装深度学习依赖: pip install torch transformers")
        print("2. 配置模型路径: 编辑 config.yaml")
        print("3. 运行完整评估: python run_evaluation.py --model your_model")
        return True
    else:
        print("❌ 部分测试失败，请检查配置和数据文件。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
