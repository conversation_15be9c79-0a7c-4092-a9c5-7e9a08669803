#!/usr/bin/env python3
"""
评估框架演示脚本
展示如何使用评估框架进行模型评估
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from data_loader import create_data_loader
from model_inference import create_model_manager
from evaluation_metrics import MetricsCalculator


def demo_data_loading():
    """演示数据加载功能"""
    print("🔍 演示数据加载功能")
    print("=" * 50)
    
    # 创建数据加载器
    loader = create_data_loader()
    
    # 显示所有可用数据集
    print("📊 可用数据集:")
    for dataset_name, config in loader.datasets_config.items():
        print(f"  - {dataset_name}: {config['description']}")
    
    # 加载一个示例数据集
    print(f"\n📖 加载语音数据集示例...")
    try:
        samples = loader.load_dataset('phoneme', sample_size=3)
        print(f"✓ 成功加载 {len(samples)} 个样本")
        
        if samples:
            sample = samples[0]
            print(f"\n📝 样本示例:")
            print(f"  样本ID: {sample.sample_id}")
            print(f"  数据集: {sample.dataset_name}")
            print(f"  指令: {sample.instruction[:100]}...")
            print(f"  输入: {sample.input[:100]}...")
            print(f"  输出: {sample.output[:100]}...")
            
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
    
    print()


def demo_evaluation_metrics():
    """演示评估指标功能"""
    print("📊 演示评估指标功能")
    print("=" * 50)
    
    # 创建评估器
    calculator = MetricsCalculator()
    
    # 示例文本
    reference = """1. **问题判断**：正常发展
2. **问题分析**：1岁6个月的儿童正处于语言快速发展阶段，发音尚未完全清晰，尤其是辅音如"l"较难掌握。
3. **年龄适宜性**：在词汇爆发期，发音不准确是正常的，多数孩子会在2-3岁逐渐改善。
4. **指导建议**：家长可多与孩子进行语音互动，重复正确发音，鼓励孩子模仿。
5. **随访建议**：若到2岁仍无法发出"l"音或发音明显异常，建议咨询语言治疗师。"""
    
    candidate = """这个年龄段的孩子发音不清楚是很正常的现象。建议家长：
1. 多和孩子说话，给孩子提供丰富的语言环境
2. 耐心纠正，但不要过度强调错误
3. 通过游戏和日常对话来练习发音
4. 如果担心可以咨询专业的语言治疗师
总的来说，大部分孩子会随着年龄增长自然改善发音问题。"""
    
    print("🎯 评估示例:")
    print(f"参考答案: {reference[:100]}...")
    print(f"模型回答: {candidate[:100]}...")
    
    # 执行评估
    result = calculator.evaluate_single(reference, candidate, "年龄: 1岁6个月")
    
    print(f"\n📈 评估结果:")
    print(f"  BLEU分数: {result.bleu_score:.3f}")
    print(f"  ROUGE-1: {result.rouge_1:.3f}")
    print(f"  ROUGE-2: {result.rouge_2:.3f}")
    print(f"  ROUGE-L: {result.rouge_l:.3f}")
    print(f"  BERTScore F1: {result.bert_score_f1:.3f}")
    print(f"  安全性分数: {result.safety_score:.3f}")
    print(f"  专业准确性: {result.professional_accuracy:.3f}")
    print(f"  综合分数: {result.overall_score:.3f}")
    
    # 显示详细信息
    if result.detailed_scores:
        safety_details = result.detailed_scores.get('safety_details', {})
        professional_details = result.detailed_scores.get('professional_details', {})
        
        print(f"\n🔍 详细分析:")
        print(f"  安全问题数量: {safety_details.get('issue_count', 0)}")
        print(f"  专业术语使用: {len(professional_details.get('used_professional_terms', []))}")
        print(f"  结构化分数: {professional_details.get('structure_score', 0):.3f}")
    
    print()


def demo_prompt_formatting():
    """演示提示词格式化"""
    print("💬 演示提示词格式化")
    print("=" * 50)
    
    # 创建模型管理器
    manager = create_model_manager()
    
    # 示例指令和输入
    instruction = "你是一位专业的婴幼儿语音构音评估专家。请根据家长提供的孩子语音发展情况，进行专业的评估和指导。"
    input_text = "孩子信息：1岁6个月，女孩，发展阶段：词汇爆发期\n问题描述：我家女孩1岁6个月了，孩子说'绿'时发音有问题，感觉是发成了其他的音。"
    
    # 格式化提示词
    prompt = manager.format_prompt(instruction, input_text)
    
    print("📝 格式化的提示词:")
    print("-" * 30)
    print(prompt)
    print("-" * 30)
    print(f"提示词长度: {len(prompt)} 字符")
    
    print()


def demo_dataset_statistics():
    """演示数据集统计功能"""
    print("📊 演示数据集统计功能")
    print("=" * 50)
    
    loader = create_data_loader()
    
    # 获取几个数据集的统计信息
    datasets_to_check = ['phoneme', 'scene', 'meal']
    
    for dataset_name in datasets_to_check:
        try:
            stats = loader.get_dataset_stats(dataset_name)
            print(f"\n📈 {dataset_name} 数据集统计:")
            print(f"  总样本数: {stats['total_samples']}")
            print(f"  平均指令长度: {stats['avg_instruction_length']:.1f} 字符")
            print(f"  平均输入长度: {stats['avg_input_length']:.1f} 字符")
            print(f"  平均输出长度: {stats['avg_output_length']:.1f} 字符")
            print(f"  描述: {stats['dataset_description']}")
        except Exception as e:
            print(f"❌ 获取 {dataset_name} 统计信息失败: {e}")
    
    print()


def main():
    """主演示函数"""
    print("🚀 婴幼儿领域大模型评估框架演示")
    print("=" * 60)
    print()
    
    demos = [
        ("数据加载", demo_data_loading),
        ("评估指标", demo_evaluation_metrics),
        ("提示词格式化", demo_prompt_formatting),
        ("数据集统计", demo_dataset_statistics),
    ]
    
    for demo_name, demo_func in demos:
        try:
            demo_func()
        except Exception as e:
            print(f"❌ {demo_name} 演示失败: {e}")
            print()
    
    print("🎉 演示完成！")
    print("\n📝 下一步操作:")
    print("1. 运行快速测试: python scripts/quick_test.py")
    print("2. 配置你的模型: 编辑 config.yaml")
    print("3. 开始评估: python run_evaluation.py --model your_model --sample-size 10")
    print("4. 分析结果: python scripts/analyze_results.py results.json --plot")


if __name__ == "__main__":
    main()
