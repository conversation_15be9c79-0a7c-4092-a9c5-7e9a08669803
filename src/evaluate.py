"""
主评估脚本
整合所有模块，执行大模型评估
"""

import os
import json
import yaml
import argparse
import pandas as pd
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any
from tqdm import tqdm

from data_loader import DatasetLoader, DatasetSample
from model_inference import ModelManager, InferenceResult
from evaluation_metrics import MetricsCalculator, EvaluationResult


class EvaluationRunner:
    """评估运行器"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """初始化评估运行器"""
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)
        
        self.data_loader = DatasetLoader(self.config)
        self.model_manager = ModelManager(self.config)
        self.metrics_calculator = MetricsCalculator()
        
        # 创建输出目录
        self.output_dir = Path(self.config['evaluation']['output_dir'])
        self.output_dir.mkdir(exist_ok=True)
        
        # 评估配置
        self.eval_config = self.config['evaluation']
        self.sample_size = self.eval_config.get('sample_size', 100)
        
    def evaluate_model_on_dataset(self, model_name: str, dataset_name: str) -> Dict[str, Any]:
        """在单个数据集上评估模型"""
        print(f"\n🔍 评估模型 {model_name} 在数据集 {dataset_name} 上的表现")
        
        # 加载数据
        samples = self.data_loader.load_dataset(dataset_name, self.sample_size)
        if not samples:
            print(f"❌ 数据集 {dataset_name} 为空")
            return {}
        
        print(f"📊 加载了 {len(samples)} 个样本")
        
        # 获取推理引擎
        engine = self.model_manager.get_engine(model_name)
        
        # 执行推理
        inference_results = []
        evaluation_results = []
        
        print("🚀 开始推理...")
        for sample in tqdm(samples, desc="推理进度"):
            # 格式化提示词
            prompt = self.model_manager.format_prompt(sample.instruction, sample.input)
            
            # 执行推理
            inference_result = engine.generate(prompt)
            inference_results.append(inference_result)
            
            # 评估结果
            context = f"数据集: {dataset_name}, 样本ID: {sample.sample_id}"
            eval_result = self.metrics_calculator.evaluate_single(
                reference=sample.output,
                candidate=inference_result.generated_text,
                context=context
            )
            evaluation_results.append(eval_result)
        
        # 计算平均指标
        avg_metrics = self._calculate_average_metrics(evaluation_results)
        
        # 统计推理时间
        total_inference_time = sum(r.inference_time for r in inference_results)
        avg_inference_time = total_inference_time / len(inference_results)
        
        # 统计错误
        error_count = sum(1 for r in inference_results if r.error)
        
        result_summary = {
            'model_name': model_name,
            'dataset_name': dataset_name,
            'sample_count': len(samples),
            'error_count': error_count,
            'success_rate': (len(samples) - error_count) / len(samples),
            'avg_inference_time': avg_inference_time,
            'total_inference_time': total_inference_time,
            'metrics': avg_metrics,
            'detailed_results': {
                'samples': [self._sample_to_dict(s) for s in samples],
                'inference_results': [self._inference_result_to_dict(r) for r in inference_results],
                'evaluation_results': [self._evaluation_result_to_dict(r) for r in evaluation_results]
            }
        }
        
        return result_summary
    
    def evaluate_model_on_all_datasets(self, model_name: str) -> Dict[str, Any]:
        """在所有数据集上评估模型"""
        print(f"\n🎯 开始评估模型: {model_name}")
        
        all_results = {}
        dataset_summaries = []
        
        for dataset_name in self.config['datasets'].keys():
            try:
                result = self.evaluate_model_on_dataset(model_name, dataset_name)
                if result:
                    all_results[dataset_name] = result
                    dataset_summaries.append({
                        'dataset_name': dataset_name,
                        'sample_count': result['sample_count'],
                        'success_rate': result['success_rate'],
                        'overall_score': result['metrics']['overall_score'],
                        'bleu_score': result['metrics']['bleu_score'],
                        'rouge_l': result['metrics']['rouge_l'],
                        'safety_score': result['metrics']['safety_score'],
                        'professional_accuracy': result['metrics']['professional_accuracy']
                    })
            except Exception as e:
                print(f"❌ 评估数据集 {dataset_name} 时出错: {e}")
                continue
        
        # 计算总体统计
        if dataset_summaries:
            overall_metrics = {
                'avg_overall_score': sum(s['overall_score'] for s in dataset_summaries) / len(dataset_summaries),
                'avg_bleu_score': sum(s['bleu_score'] for s in dataset_summaries) / len(dataset_summaries),
                'avg_rouge_l': sum(s['rouge_l'] for s in dataset_summaries) / len(dataset_summaries),
                'avg_safety_score': sum(s['safety_score'] for s in dataset_summaries) / len(dataset_summaries),
                'avg_professional_accuracy': sum(s['professional_accuracy'] for s in dataset_summaries) / len(dataset_summaries),
                'avg_success_rate': sum(s['success_rate'] for s in dataset_summaries) / len(dataset_summaries)
            }
        else:
            overall_metrics = {}
        
        final_result = {
            'model_name': model_name,
            'evaluation_time': datetime.now().isoformat(),
            'dataset_count': len(dataset_summaries),
            'overall_metrics': overall_metrics,
            'dataset_summaries': dataset_summaries,
            'detailed_results': all_results
        }
        
        return final_result
    
    def _calculate_average_metrics(self, results: List[EvaluationResult]) -> Dict[str, float]:
        """计算平均指标"""
        if not results:
            return {}
        
        return {
            'bleu_score': sum(r.bleu_score for r in results) / len(results),
            'rouge_1': sum(r.rouge_1 for r in results) / len(results),
            'rouge_2': sum(r.rouge_2 for r in results) / len(results),
            'rouge_l': sum(r.rouge_l for r in results) / len(results),
            'bert_score_f1': sum(r.bert_score_f1 for r in results) / len(results),
            'safety_score': sum(r.safety_score for r in results) / len(results),
            'professional_accuracy': sum(r.professional_accuracy for r in results) / len(results),
            'overall_score': sum(r.overall_score for r in results) / len(results)
        }
    
    def _sample_to_dict(self, sample: DatasetSample) -> Dict[str, Any]:
        """将样本转换为字典"""
        return {
            'sample_id': sample.sample_id,
            'dataset_name': sample.dataset_name,
            'instruction': sample.instruction,
            'input': sample.input,
            'output': sample.output,
            'metadata': sample.metadata
        }
    
    def _inference_result_to_dict(self, result: InferenceResult) -> Dict[str, Any]:
        """将推理结果转换为字典"""
        return {
            'input_text': result.input_text,
            'generated_text': result.generated_text,
            'inference_time': result.inference_time,
            'model_name': result.model_name,
            'error': result.error
        }
    
    def _evaluation_result_to_dict(self, result: EvaluationResult) -> Dict[str, Any]:
        """将评估结果转换为字典"""
        return {
            'bleu_score': result.bleu_score,
            'rouge_1': result.rouge_1,
            'rouge_2': result.rouge_2,
            'rouge_l': result.rouge_l,
            'bert_score_f1': result.bert_score_f1,
            'safety_score': result.safety_score,
            'professional_accuracy': result.professional_accuracy,
            'overall_score': result.overall_score,
            'detailed_scores': result.detailed_scores
        }
    
    def save_results(self, results: Dict[str, Any], filename: str = None):
        """保存评估结果"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"evaluation_results_{results['model_name']}_{timestamp}.json"
        
        output_path = self.output_dir / filename
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"💾 评估结果已保存到: {output_path}")
        
        # 同时保存简化的CSV报告
        self._save_csv_report(results, output_path.with_suffix('.csv'))
    
    def _save_csv_report(self, results: Dict[str, Any], csv_path: Path):
        """保存CSV格式的简化报告"""
        if 'dataset_summaries' not in results:
            return
        
        df = pd.DataFrame(results['dataset_summaries'])
        df.to_csv(csv_path, index=False, encoding='utf-8')
        print(f"📊 CSV报告已保存到: {csv_path}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="婴幼儿领域大模型评估")
    parser.add_argument('--config', default='config.yaml', help='配置文件路径')
    parser.add_argument('--model', required=True, help='要评估的模型名称')
    parser.add_argument('--dataset', help='指定数据集名称（可选，默认评估所有数据集）')
    parser.add_argument('--sample-size', type=int, help='每个数据集的样本数量')
    parser.add_argument('--output', help='输出文件名')
    
    args = parser.parse_args()
    
    # 创建评估运行器
    runner = EvaluationRunner(args.config)
    
    # 覆盖配置中的样本大小
    if args.sample_size:
        runner.sample_size = args.sample_size
    
    try:
        if args.dataset:
            # 评估单个数据集
            results = runner.evaluate_model_on_dataset(args.model, args.dataset)
            if results:
                print(f"\n📈 评估完成！")
                print(f"总体分数: {results['metrics']['overall_score']:.3f}")
                print(f"成功率: {results['success_rate']:.1%}")
        else:
            # 评估所有数据集
            results = runner.evaluate_model_on_all_datasets(args.model)
            if results['overall_metrics']:
                print(f"\n🎉 全部评估完成！")
                print(f"平均总体分数: {results['overall_metrics']['avg_overall_score']:.3f}")
                print(f"平均成功率: {results['overall_metrics']['avg_success_rate']:.1%}")
                print(f"评估数据集数量: {results['dataset_count']}")
        
        # 保存结果
        if results:
            runner.save_results(results, args.output)
            
    except KeyboardInterrupt:
        print("\n⏹️ 评估被用户中断")
    except Exception as e:
        print(f"\n❌ 评估过程中出现错误: {e}")
        raise


if __name__ == "__main__":
    main()
