"""
婴幼儿领域数据集加载器
支持多种格式的数据集统一加载和处理
"""

import json
import jsonlines
import pandas as pd
from typing import List, Dict, Any, Optional
from pathlib import Path
import random
from dataclasses import dataclass


@dataclass
class DatasetSample:
    """数据样本结构"""
    instruction: str
    input: str
    output: str
    dataset_name: str
    sample_id: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class DatasetLoader:
    """统一数据集加载器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.datasets_config = config.get('datasets', {})
        
    def load_dataset(self, dataset_name: str, sample_size: Optional[int] = None) -> List[DatasetSample]:
        """
        加载指定数据集
        
        Args:
            dataset_name: 数据集名称
            sample_size: 采样数量，None表示加载全部
            
        Returns:
            数据样本列表
        """
        if dataset_name not in self.datasets_config:
            raise ValueError(f"未找到数据集配置: {dataset_name}")
            
        dataset_config = self.datasets_config[dataset_name]
        file_path = Path(dataset_config['path'])
        file_format = dataset_config['format']
        
        if not file_path.exists():
            raise FileNotFoundError(f"数据集文件不存在: {file_path}")
            
        # 根据格式加载数据
        if file_format == 'jsonl':
            samples = self._load_jsonl(file_path, dataset_name)
        elif file_format == 'json':
            samples = self._load_json(file_path, dataset_name)
        else:
            raise ValueError(f"不支持的文件格式: {file_format}")
            
        # 采样
        if sample_size and sample_size > 0 and len(samples) > sample_size:
            samples = random.sample(samples, sample_size)
            
        return samples
    
    def _load_jsonl(self, file_path: Path, dataset_name: str) -> List[DatasetSample]:
        """加载JSONL格式数据"""
        samples = []
        
        with jsonlines.open(file_path, 'r') as reader:
            for idx, item in enumerate(reader):
                sample = DatasetSample(
                    instruction=item.get('instruction', ''),
                    input=item.get('input', ''),
                    output=item.get('output', ''),
                    dataset_name=dataset_name,
                    sample_id=item.get('record_id', f"{dataset_name}_{idx}"),
                    metadata=self._extract_metadata(item)
                )
                samples.append(sample)
                
        return samples
    
    def _load_json(self, file_path: Path, dataset_name: str) -> List[DatasetSample]:
        """加载JSON格式数据"""
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            
        samples = []
        
        # 处理不同的JSON结构
        if isinstance(data, list):
            # 直接是样本列表
            data_items = data
        elif isinstance(data, dict):
            # 可能包含metadata和data字段
            if 'data' in data:
                data_items = data['data']
            else:
                # 假设整个dict就是一个样本
                data_items = [data]
        else:
            raise ValueError(f"不支持的JSON结构: {type(data)}")
            
        for idx, item in enumerate(data_items):
            sample = DatasetSample(
                instruction=item.get('instruction', ''),
                input=item.get('input', ''),
                output=item.get('output', ''),
                dataset_name=dataset_name,
                sample_id=item.get('id', f"{dataset_name}_{idx}"),
                metadata=self._extract_metadata(item)
            )
            samples.append(sample)
            
        return samples
    
    def _extract_metadata(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """提取元数据"""
        metadata = {}
        
        # 常见的元数据字段
        metadata_fields = ['source', 'age_group', 'type', 'category', 'difficulty']
        
        for field in metadata_fields:
            if field in item:
                metadata[field] = item[field]
                
        # 如果有专门的metadata字段
        if 'metadata' in item:
            metadata.update(item['metadata'])
            
        return metadata
    
    def load_all_datasets(self, sample_size: Optional[int] = None) -> Dict[str, List[DatasetSample]]:
        """
        加载所有数据集
        
        Args:
            sample_size: 每个数据集的采样数量
            
        Returns:
            数据集名称到样本列表的映射
        """
        all_datasets = {}
        
        for dataset_name in self.datasets_config.keys():
            try:
                samples = self.load_dataset(dataset_name, sample_size)
                all_datasets[dataset_name] = samples
                print(f"✓ 加载数据集 {dataset_name}: {len(samples)} 个样本")
            except Exception as e:
                print(f"✗ 加载数据集 {dataset_name} 失败: {e}")
                
        return all_datasets
    
    def get_dataset_stats(self, dataset_name: str) -> Dict[str, Any]:
        """获取数据集统计信息"""
        samples = self.load_dataset(dataset_name)
        
        stats = {
            'total_samples': len(samples),
            'avg_instruction_length': sum(len(s.instruction) for s in samples) / len(samples),
            'avg_input_length': sum(len(s.input) for s in samples) / len(samples),
            'avg_output_length': sum(len(s.output) for s in samples) / len(samples),
            'dataset_description': self.datasets_config[dataset_name].get('description', ''),
        }
        
        return stats


def create_data_loader(config_path: str = "config.yaml") -> DatasetLoader:
    """创建数据加载器实例"""
    import yaml
    
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
        
    return DatasetLoader(config)


if __name__ == "__main__":
    # 测试数据加载器
    loader = create_data_loader()
    
    # 加载单个数据集
    phoneme_samples = loader.load_dataset('phoneme', sample_size=5)
    print(f"语音数据集样本: {len(phoneme_samples)}")
    
    if phoneme_samples:
        sample = phoneme_samples[0]
        print(f"样本ID: {sample.sample_id}")
        print(f"指令: {sample.instruction[:100]}...")
        print(f"输入: {sample.input[:100]}...")
        print(f"输出: {sample.output[:100]}...")
    
    # 获取统计信息
    stats = loader.get_dataset_stats('phoneme')
    print(f"数据集统计: {stats}")
