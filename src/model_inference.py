"""
大模型推理模块
支持本地模型和API调用的统一接口
"""

import torch
from transformers import AutoTokenizer, AutoModelForCausalLM, GenerationConfig
from typing import List, Dict, Any, Optional, Union
import requests
import json
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass


@dataclass
class InferenceResult:
    """推理结果"""
    input_text: str
    generated_text: str
    full_response: str
    inference_time: float
    model_name: str
    error: Optional[str] = None


class BaseInferenceEngine(ABC):
    """推理引擎基类"""
    
    def __init__(self, model_config: Dict[str, Any]):
        self.model_config = model_config
        self.model_name = model_config['model_name']
        
    @abstractmethod
    def generate(self, prompt: str) -> InferenceResult:
        """生成文本"""
        pass
    
    @abstractmethod
    def batch_generate(self, prompts: List[str]) -> List[InferenceResult]:
        """批量生成文本"""
        pass


class LocalInferenceEngine(BaseInferenceEngine):
    """本地模型推理引擎"""
    
    def __init__(self, model_config: Dict[str, Any]):
        super().__init__(model_config)
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.model = None
        self.tokenizer = None
        self._load_model()
        
    def _load_model(self):
        """加载模型和分词器"""
        print(f"正在加载模型: {self.model_name}")
        
        try:
            # 加载分词器
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.model_name,
                trust_remote_code=True,
                padding_side='left'
            )
            
            # 设置pad_token
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            # 加载模型
            self.model = AutoModelForCausalLM.from_pretrained(
                self.model_name,
                torch_dtype=torch.float16 if self.device.type == 'cuda' else torch.float32,
                device_map="auto" if self.device.type == 'cuda' else None,
                trust_remote_code=True
            )
            
            if self.device.type == 'cpu':
                self.model = self.model.to(self.device)
                
            self.model.eval()
            print(f"✓ 模型加载成功，设备: {self.device}")
            
        except Exception as e:
            print(f"✗ 模型加载失败: {e}")
            raise
    
    def generate(self, prompt: str) -> InferenceResult:
        """单个文本生成"""
        start_time = time.time()
        
        try:
            # 编码输入
            inputs = self.tokenizer(
                prompt,
                return_tensors="pt",
                truncation=True,
                max_length=self.model_config.get('max_input_length', 1024)
            ).to(self.device)
            
            # 生成配置
            generation_config = GenerationConfig(
                max_new_tokens=self.model_config.get('max_new_tokens', 512),
                temperature=self.model_config.get('temperature', 0.7),
                top_p=self.model_config.get('top_p', 0.9),
                do_sample=True,
                pad_token_id=self.tokenizer.pad_token_id,
                eos_token_id=self.tokenizer.eos_token_id,
            )
            
            # 生成文本
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    generation_config=generation_config
                )
            
            # 解码输出
            full_response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            generated_text = full_response[len(prompt):].strip()
            
            inference_time = time.time() - start_time
            
            return InferenceResult(
                input_text=prompt,
                generated_text=generated_text,
                full_response=full_response,
                inference_time=inference_time,
                model_name=self.model_name
            )
            
        except Exception as e:
            inference_time = time.time() - start_time
            return InferenceResult(
                input_text=prompt,
                generated_text="",
                full_response="",
                inference_time=inference_time,
                model_name=self.model_name,
                error=str(e)
            )
    
    def batch_generate(self, prompts: List[str]) -> List[InferenceResult]:
        """批量生成文本"""
        results = []
        
        # 简单的逐个处理，可以优化为真正的批处理
        for prompt in prompts:
            result = self.generate(prompt)
            results.append(result)
            
        return results


class APIInferenceEngine(BaseInferenceEngine):
    """API调用推理引擎"""
    
    def __init__(self, model_config: Dict[str, Any]):
        super().__init__(model_config)
        self.api_url = model_config.get('api_url', '')
        self.api_key = model_config.get('api_key', '')
        self.headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {self.api_key}' if self.api_key else ''
        }
    
    def generate(self, prompt: str) -> InferenceResult:
        """API调用生成文本"""
        start_time = time.time()
        
        try:
            payload = {
                'model': self.model_name,
                'messages': [
                    {'role': 'user', 'content': prompt}
                ],
                'temperature': self.model_config.get('temperature', 0.7),
                'max_tokens': self.model_config.get('max_new_tokens', 512),
                'top_p': self.model_config.get('top_p', 0.9)
            }
            
            response = requests.post(
                self.api_url,
                headers=self.headers,
                json=payload,
                timeout=60
            )
            
            response.raise_for_status()
            result_data = response.json()
            
            # 解析响应（根据具体API格式调整）
            generated_text = result_data['choices'][0]['message']['content']
            
            inference_time = time.time() - start_time
            
            return InferenceResult(
                input_text=prompt,
                generated_text=generated_text,
                full_response=json.dumps(result_data),
                inference_time=inference_time,
                model_name=self.model_name
            )
            
        except Exception as e:
            inference_time = time.time() - start_time
            return InferenceResult(
                input_text=prompt,
                generated_text="",
                full_response="",
                inference_time=inference_time,
                model_name=self.model_name,
                error=str(e)
            )
    
    def batch_generate(self, prompts: List[str]) -> List[InferenceResult]:
        """批量API调用"""
        results = []
        
        for prompt in prompts:
            result = self.generate(prompt)
            results.append(result)
            # 添加延迟避免API限流
            time.sleep(0.1)
            
        return results


class ModelManager:
    """模型管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.models_config = config.get('models', {})
        self.engines = {}
    
    def get_engine(self, model_name: str) -> BaseInferenceEngine:
        """获取推理引擎"""
        if model_name not in self.engines:
            if model_name not in self.models_config:
                raise ValueError(f"未找到模型配置: {model_name}")
                
            model_config = self.models_config[model_name]
            model_type = model_config.get('model_type', 'local')
            
            if model_type == 'local':
                engine = LocalInferenceEngine(model_config)
            elif model_type == 'api':
                engine = APIInferenceEngine(model_config)
            else:
                raise ValueError(f"不支持的模型类型: {model_type}")
                
            self.engines[model_name] = engine
            
        return self.engines[model_name]
    
    def format_prompt(self, instruction: str, input_text: str) -> str:
        """格式化提示词"""
        if input_text.strip():
            prompt = f"### 指令:\n{instruction}\n\n### 输入:\n{input_text}\n\n### 输出:\n"
        else:
            prompt = f"### 指令:\n{instruction}\n\n### 输出:\n"
        return prompt


def create_model_manager(config_path: str = "config.yaml") -> ModelManager:
    """创建模型管理器实例"""
    import yaml
    
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
        
    return ModelManager(config)


if __name__ == "__main__":
    # 测试模型推理
    manager = create_model_manager()
    
    # 测试提示词格式化
    instruction = "你是一位专业的婴幼儿发展专家，请回答以下问题。"
    input_text = "我的孩子2岁了，还不会说话，这正常吗？"
    
    prompt = manager.format_prompt(instruction, input_text)
    print("格式化的提示词:")
    print(prompt)
