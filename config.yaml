# 婴幼儿领域大模型评估配置文件

# 数据集配置
datasets:
  phoneme:
    path: "datasets/1-phoneme/enhanced_speech_assessment_instruction_format.jsonl"
    format: "jsonl"
    description: "语音构音评估数据集"
    
  scene:
    path: "datasets/2-scene/cribhd_instruction_dataset.json"
    format: "json"
    description: "场景监测数据集"
    
  meal:
    path: "datasets/3-meal/infant_nutrition_alpaca_format_20250728_144636.json"
    format: "json"
    description: "营养喂养数据集"
    
  body:
    path: "datasets/4-body/consolidated_growth_monitoring_dataset.json"
    format: "json"
    description: "生长发育数据集"
    
  pronunciation:
    path: "datasets/5-pronunciation/final_training_data_all_data_20250728_151919.json"
    format: "json"
    description: "发音训练数据集"
    
  gross_motor:
    path: "datasets/6-gross_motor/consolidated_infant_motor_dataset.json"
    format: "json"
    description: "大运动发展数据集"
    
  fine_motor:
    path: "datasets/7-fine-motor/comprehensive_instruction_dataset.jsonl"
    format: "jsonl"
    description: "精细运动数据集"
    
  emotion:
    path: "datasets/9-emotion/infant_emotion_instruction_tuning_dataset.json"
    format: "json"
    description: "情绪发展数据集"
    
  language:
    path: "datasets/10-language/unified_instruction_dataset.json"
    format: "json"
    description: "语言发展数据集"

# 模型配置
models:
  qwen_7b_chat:
    model_name: "Qwen/Qwen-7B-Chat"
    model_type: "local"  # local 或 api
    max_length: 2048
    temperature: 0.7
    top_p: 0.9
    
  qwen_14b_chat:
    model_name: "Qwen/Qwen-14B-Chat"
    model_type: "local"
    max_length: 2048
    temperature: 0.7
    top_p: 0.9

# 评估配置
evaluation:
  sample_size: 100  # 每个数据集评估的样本数量，-1表示全部
  batch_size: 8
  metrics:
    - "bleu"
    - "rouge"
    - "bert_score"
    - "safety_score"
    - "professional_accuracy"
  
  output_dir: "evaluation_results"
  save_predictions: true
  save_detailed_results: true

# 评估指标权重
metric_weights:
  bleu: 0.2
  rouge_l: 0.2
  bert_score: 0.2
  safety_score: 0.2
  professional_accuracy: 0.2

# 硬件配置
hardware:
  device: "auto"  # auto, cpu, cuda
  fp16: true
  gradient_checkpointing: true
