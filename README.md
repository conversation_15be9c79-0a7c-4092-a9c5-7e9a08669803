# 婴幼儿领域大模型评估框架

这是一个专门为婴幼儿领域大模型评估设计的综合性评估框架，支持对Qwen等7B模型在9个不同方向数据集上的全面评估。

## 🎯 功能特点

- **多数据集支持**: 支持9个婴幼儿领域数据集的统一评估
- **多模型兼容**: 支持本地模型和API调用的统一接口
- **多维度评估**: 包含BLEU、ROUGE、安全性、专业准确性等多种指标
- **可视化分析**: 提供丰富的图表和报告生成功能
- **易于扩展**: 模块化设计，便于添加新的数据集和评估指标

## 📊 支持的数据集

| 数据集 | 描述 | 样本数量 | 格式 |
|--------|------|----------|------|
| phoneme | 语音构音评估数据集 | 1000+ | JSONL |
| scene | 场景监测数据集 | 9000+ | JSON |
| meal | 营养喂养数据集 | 1000+ | JSON |
| body | 生长发育数据集 | 700+ | JSON |
| pronunciation | 发音训练数据集 | 1000+ | JSON |
| gross_motor | 大运动发展数据集 | 500+ | JSON |
| fine_motor | 精细运动数据集 | 1000+ | JSONL |
| emotion | 情绪发展数据集 | 1000+ | JSON |
| language | 语言发展数据集 | 500+ | JSON |

## 🚀 快速开始

### 1. 环境准备

```bash
# 克隆项目（如果适用）
git clone <repository-url>
cd evaluation

# 安装依赖
pip install -r requirements.txt

# 下载必要的NLTK数据
python -c "import nltk; nltk.download('punkt')"
```

### 2. 配置模型

编辑 `config.yaml` 文件，配置你要评估的模型：

```yaml
models:
  qwen_7b_chat:
    model_name: "Qwen/Qwen-7B-Chat"
    model_type: "local"  # 或 "api"
    max_length: 2048
    temperature: 0.7
    top_p: 0.9
```

### 3. 运行快速测试

```bash
# 验证框架是否正常工作
python scripts/quick_test.py
```

### 4. 开始评估

```bash
# 评估所有数据集
python run_evaluation.py --model qwen_7b_chat

# 评估特定数据集
python run_evaluation.py --model qwen_7b_chat --dataset phoneme

# 限制样本数量（用于快速测试）
python run_evaluation.py --model qwen_7b_chat --sample-size 10
```

## 📈 评估指标

### 1. 文本质量指标
- **BLEU**: 基于n-gram的文本相似度
- **ROUGE**: 基于召回率的文本质量评估
- **BERTScore**: 基于语义相似度的评估

### 2. 专业性指标
- **安全性评估**: 检测潜在的安全风险建议
- **专业准确性**: 评估专业术语使用和建议质量
- **结构化程度**: 评估回答的组织结构

### 3. 综合指标
- **总体分数**: 各项指标的加权平均
- **成功率**: 成功生成回答的比例
- **推理时间**: 模型响应时间统计

## 📊 结果分析

### 查看评估结果

```bash
# 分析评估结果
python scripts/analyze_results.py evaluation_results/evaluation_results_qwen_7b_chat_20250728_143022.json

# 生成可视化图表
python scripts/analyze_results.py results.json --plot

# 导出CSV报告
python scripts/analyze_results.py results.json --csv
```

### 结果文件结构

```
evaluation_results/
├── evaluation_results_model_timestamp.json  # 详细结果
├── evaluation_results_model_timestamp.csv   # 简化报告
└── analysis_plots/                          # 可视化图表
    ├── model_overview.png
    ├── model_heatmap.png
    └── model_stats.png
```

## 🔧 高级配置

### 自定义评估指标权重

在 `config.yaml` 中调整指标权重：

```yaml
metric_weights:
  bleu: 0.2
  rouge_l: 0.2
  bert_score: 0.2
  safety_score: 0.2
  professional_accuracy: 0.2
```

### 添加新的数据集

1. 将数据集文件放入 `datasets/` 目录
2. 在 `config.yaml` 中添加数据集配置：

```yaml
datasets:
  new_dataset:
    path: "datasets/new_dataset/data.json"
    format: "json"
    description: "新数据集描述"
```

### 添加新的模型

在 `config.yaml` 中添加模型配置：

```yaml
models:
  new_model:
    model_name: "path/to/model"
    model_type: "local"  # 或 "api"
    max_length: 2048
    temperature: 0.7
```

## 📝 使用示例

### 评估Qwen-7B模型

```bash
# 完整评估
python run_evaluation.py --model qwen_7b_chat

# 快速测试（每个数据集10个样本）
python run_evaluation.py --model qwen_7b_chat --sample-size 10

# 只评估语音相关数据集
python run_evaluation.py --model qwen_7b_chat --dataset phoneme
```

### 批量评估多个模型

```bash
# 创建批量评估脚本
for model in qwen_7b_chat qwen_14b_chat; do
    python run_evaluation.py --model $model --sample-size 50
done
```

## 🛠️ 开发指南

### 项目结构

```
evaluation/
├── src/                    # 核心代码
│   ├── data_loader.py     # 数据加载模块
│   ├── model_inference.py # 模型推理模块
│   ├── evaluation_metrics.py # 评估指标模块
│   └── evaluate.py        # 主评估脚本
├── scripts/               # 辅助脚本
│   ├── quick_test.py      # 快速测试
│   └── analyze_results.py # 结果分析
├── datasets/              # 数据集目录
├── evaluation_results/    # 评估结果目录
├── config.yaml           # 配置文件
├── requirements.txt      # 依赖列表
└── README.md            # 说明文档
```

### 扩展评估指标

1. 在 `src/evaluation_metrics.py` 中添加新的评估器类
2. 在 `MetricsCalculator` 中集成新指标
3. 更新配置文件中的指标权重

### 添加新的推理引擎

1. 继承 `BaseInferenceEngine` 类
2. 实现 `generate` 和 `batch_generate` 方法
3. 在 `ModelManager` 中注册新引擎

## ⚠️ 注意事项

1. **模型加载**: 本地模型需要足够的GPU内存，建议使用16GB以上显存
2. **数据隐私**: 评估过程中请注意保护数据隐私
3. **结果解释**: 评估结果仅供参考，实际应用需结合专业判断
4. **版本兼容**: 建议使用Python 3.8+和PyTorch 2.0+

## 📞 技术支持

如有问题或建议，请：
1. 查看快速测试结果
2. 检查配置文件格式
3. 确认依赖安装完整
4. 联系项目维护者

## 📄 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。
